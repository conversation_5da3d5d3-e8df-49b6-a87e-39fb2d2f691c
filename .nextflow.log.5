Jun-01 15:24:52.240 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna
Jun-01 15:24:52.444 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
Jun-01 15:24:52.479 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
Jun-01 15:24:52.541 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-01 15:24:52.542 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-01 15:24:52.546 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-01 15:24:52.570 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-01 15:24:52.593 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-01 15:24:52.605 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-01 15:24:52.650 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
Jun-01 15:24:52.654 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7e97551f] - activable => nextflow.secret.LocalSecretsProvider@7e97551f
Jun-01 15:24:52.666 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Jun-01 15:24:53.229 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-01 15:24:53.252 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [soggy_mercator] DSL2 - revision: e9019381d3
Jun-01 15:24:53.253 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-01 15:24:53.253 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-01 15:24:53.316 [main] DEBUG nextflow.Session - Session UUID: 3a984b3b-8e8f-4cbb-b370-4c68608e01a5
Jun-01 15:24:53.316 [main] DEBUG nextflow.Session - Run name: soggy_mercator
Jun-01 15:24:53.320 [main] DEBUG nextflow.Session - Executor pool size: 32
Jun-01 15:24:53.330 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-01 15:24:53.335 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 15:24:53.422 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (84.9 GB) - Swap: 8 GB (1.7 MB)
Jun-01 15:24:53.468 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
Jun-01 15:24:53.512 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-01 15:24:53.528 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-01 15:24:53.532 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-01 15:24:53.562 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-01 15:24:53.574 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
Jun-01 15:24:53.947 [main] DEBUG nextflow.Session - Session start
Jun-01 15:24:54.650 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-01 15:24:54.687 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: false
==============================================

Jun-01 15:24:54.881 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
Jun-01 15:24:54.892 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 15:24:54.892 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 15:24:54.899 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-01 15:24:54.906 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
Jun-01 15:24:54.909 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-01 15:24:54.929 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
Jun-01 15:24:54.980 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:GTDBTK` matches process GTDBTK
Jun-01 15:24:54.981 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 15:24:54.989 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 15:24:55.000 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'GTDBTK': maxForks=0; fair=false; array=0
Jun-01 15:24:55.016 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
Jun-01 15:24:55.033 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 15:24:55.033 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 15:24:55.034 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
Jun-01 15:24:55.036 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
Jun-01 15:24:55.047 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
Jun-01 15:24:55.049 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 15:24:55.049 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 15:24:55.053 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
Jun-01 15:24:55.055 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
Jun-01 15:24:55.077 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
Jun-01 15:24:55.078 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 15:24:55.079 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 15:24:55.081 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
Jun-01 15:24:55.098 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 15:24:55.099 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 15:24:55.100 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
Jun-01 15:24:55.111 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 15:24:55.111 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 15:24:55.113 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
Jun-01 15:24:55.117 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
Jun-01 15:24:55.120 [main] DEBUG nextflow.Session - Igniting dataflow network (21)
Jun-01 15:24:55.128 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
Jun-01 15:24:55.128 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > GTDBTK
Jun-01 15:24:55.129 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-01 15:24:55.131 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-01 15:24:55.133 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-01 15:24:55.134 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-01 15:24:55.135 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
Jun-01 15:24:55.147 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
Jun-01 15:24:55.148 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
Jun-01 15:24:55.150 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_1343ce0d68727479: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Jun-01 15:24:55.151 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-01 15:24:55.152 [main] DEBUG nextflow.Session - Session await
Jun-01 15:24:55.302 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:24:55.304 [Task submitter] INFO  nextflow.Session - [a1/abe5b5] Submitted process > GTDBTK (1)
Jun-01 15:24:55.334 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:24:55.335 [Task submitter] INFO  nextflow.Session - [e8/347f3f] Submitted process > PRODIGAL (1)
Jun-01 15:25:03.006 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e8/******************************]
Jun-01 15:25:03.008 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 15:25:03.037 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 15:25:03.127 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:25:03.128 [Task submitter] INFO  nextflow.Session - [c9/c66391] Submitted process > HMMSEARCH (batch_10)
Jun-01 15:25:12.586 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/c66391b2b4cabce86b47641d66977e]
Jun-01 15:25:12.630 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:25:12.630 [Task submitter] INFO  nextflow.Session - [f6/80c527] Submitted process > HMMSEARCH (batch_29)
Jun-01 15:25:21.877 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f6/80c5275f9870d0ed1601798b996f85]
Jun-01 15:25:21.892 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:25:21.892 [Task submitter] INFO  nextflow.Session - [3a/5f4ec3] Submitted process > HMMSEARCH (batch_21)
Jun-01 15:25:32.296 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3a/5f4ec34bdced9b62f1b4091a38f717]
Jun-01 15:25:32.315 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:25:32.316 [Task submitter] INFO  nextflow.Session - [5e/854d98] Submitted process > HMMSEARCH (batch_16)
Jun-01 15:25:42.289 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/854d986373a3f2c7557c0478d3dd91]
Jun-01 15:25:42.364 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:25:42.365 [Task submitter] INFO  nextflow.Session - [af/92d3fe] Submitted process > HMMSEARCH (batch_4)
Jun-01 15:25:51.633 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/92d3fe62eff4abd0812b0bbfadf891]
Jun-01 15:25:51.654 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:25:51.654 [Task submitter] INFO  nextflow.Session - [08/4255d2] Submitted process > HMMSEARCH (batch_25)
Jun-01 15:26:00.448 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/4255d25abb4358c88a479944f19e60]
Jun-01 15:26:00.461 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:00.461 [Task submitter] INFO  nextflow.Session - [58/29f048] Submitted process > HMMSEARCH (batch_24)
Jun-01 15:26:10.105 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/58/29f04848e9be2a5aede675018879b4]
Jun-01 15:26:10.143 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:10.144 [Task submitter] INFO  nextflow.Session - [78/2acda3] Submitted process > HMMSEARCH (batch_13)
Jun-01 15:26:19.602 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/78/2acda36500f07e1f57dcf0f7eff944]
Jun-01 15:26:19.629 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:19.630 [Task submitter] INFO  nextflow.Session - [ae/cbb206] Submitted process > HMMSEARCH (batch_32)
Jun-01 15:26:26.941 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ae/cbb20632ff5424ff4556650820cc96]
Jun-01 15:26:26.951 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:26.952 [Task submitter] INFO  nextflow.Session - [9c/d8ac71] Submitted process > HMMSEARCH (batch_5)
Jun-01 15:26:34.100 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9c/d8ac71cea010feb54f11a406523b50]
Jun-01 15:26:34.118 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:34.118 [Task submitter] INFO  nextflow.Session - [fa/356cd0] Submitted process > HMMSEARCH (batch_15)
Jun-01 15:26:40.390 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fa/356cd04615d3d19d437e6a7c849357]
Jun-01 15:26:40.401 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:40.402 [Task submitter] INFO  nextflow.Session - [7a/312cbb] Submitted process > HMMSEARCH (batch_27)
Jun-01 15:26:46.708 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7a/312cbba5cd7cb1b4685b99b7e757b6]
Jun-01 15:26:46.723 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:46.723 [Task submitter] INFO  nextflow.Session - [23/875303] Submitted process > HMMSEARCH (batch_33)
Jun-01 15:26:52.794 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/23/875303a905139a7726e4b5c4882873]
Jun-01 15:26:52.815 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:52.815 [Task submitter] INFO  nextflow.Session - [91/d85697] Submitted process > HMMSEARCH (batch_14)
Jun-01 15:26:58.427 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/91/d856975b05d8ad4b3704d1348b2b22]
Jun-01 15:26:58.442 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:26:58.442 [Task submitter] INFO  nextflow.Session - [42/e5265c] Submitted process > HMMSEARCH (batch_8)
Jun-01 15:27:04.210 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/e5265ca9b95cd36956a4a8c89bb0a1]
Jun-01 15:27:04.232 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:04.232 [Task submitter] INFO  nextflow.Session - [e2/83b352] Submitted process > HMMSEARCH (batch_12)
Jun-01 15:27:10.235 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e2/83b352661d901358037fdfe72db03c]
Jun-01 15:27:10.249 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:10.250 [Task submitter] INFO  nextflow.Session - [c7/7da9e3] Submitted process > HMMSEARCH (batch_2)
Jun-01 15:27:16.405 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c7/7da9e3c937f51b370f5d70601d3181]
Jun-01 15:27:16.421 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:16.422 [Task submitter] INFO  nextflow.Session - [d3/2c64f1] Submitted process > HMMSEARCH (batch_20)
Jun-01 15:27:22.737 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d3/2c64f1aea38c09746c1f08808fd21c]
Jun-01 15:27:22.768 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:22.769 [Task submitter] INFO  nextflow.Session - [a6/31bbab] Submitted process > HMMSEARCH (batch_34)
Jun-01 15:27:29.033 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a6/31bbab30ac58462accf3250663b723]
Jun-01 15:27:29.047 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:29.047 [Task submitter] INFO  nextflow.Session - [16/223524] Submitted process > HMMSEARCH (batch_23)
Jun-01 15:27:35.590 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/2235240ff4759870fa075e74c89bcb]
Jun-01 15:27:35.603 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:35.604 [Task submitter] INFO  nextflow.Session - [a9/938c5e] Submitted process > HMMSEARCH (batch_11)
Jun-01 15:27:41.991 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/938c5efa990775c681c051fd6c84fa]
Jun-01 15:27:42.006 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:42.007 [Task submitter] INFO  nextflow.Session - [2e/5ad04e] Submitted process > HMMSEARCH (batch_17)
Jun-01 15:27:47.558 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2e/5ad04e519d6b3e29d369880a470488]
Jun-01 15:27:47.576 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:47.577 [Task submitter] INFO  nextflow.Session - [8f/97e951] Submitted process > HMMSEARCH (batch_9)
Jun-01 15:27:53.391 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8f/97e9517ff08f7dcac670311b557df0]
Jun-01 15:27:53.408 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:53.408 [Task submitter] INFO  nextflow.Session - [70/dfb846] Submitted process > HMMSEARCH (batch_30)
Jun-01 15:27:58.948 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/dfb846936b5c5ca099032c8cf97276]
Jun-01 15:27:58.972 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:27:58.972 [Task submitter] INFO  nextflow.Session - [c0/1af70e] Submitted process > HMMSEARCH (batch_26)
Jun-01 15:28:05.073 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/1af70e3702412b4ff8088ff4f48816]
Jun-01 15:28:05.088 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:05.088 [Task submitter] INFO  nextflow.Session - [22/fa1387] Submitted process > HMMSEARCH (batch_22)
Jun-01 15:28:12.349 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/22/fa13872f6a328555c18d939db7f1fa]
Jun-01 15:28:12.365 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:12.366 [Task submitter] INFO  nextflow.Session - [25/67bb70] Submitted process > HMMSEARCH (batch_1)
Jun-01 15:28:18.952 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/25/67bb70756c079221ed2cc9fb0b28d0]
Jun-01 15:28:18.995 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:18.995 [Task submitter] INFO  nextflow.Session - [b9/4d91f1] Submitted process > HMMSEARCH (batch_19)
Jun-01 15:28:24.689 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/4d91f12e2b5ba7ac36dec9f618dd2d]
Jun-01 15:28:24.708 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:24.708 [Task submitter] INFO  nextflow.Session - [45/0466d5] Submitted process > HMMSEARCH (batch_28)
Jun-01 15:28:31.219 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/45/0466d557ad6f01f4bbac8a3b304b89]
Jun-01 15:28:31.245 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:31.245 [Task submitter] INFO  nextflow.Session - [d5/83aea8] Submitted process > HMMSEARCH (batch_31)
Jun-01 15:28:38.473 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d5/83aea83efbc1e3683f58c86ed61db9]
Jun-01 15:28:38.495 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:38.496 [Task submitter] INFO  nextflow.Session - [98/f5ff64] Submitted process > HMMSEARCH (batch_3)
Jun-01 15:28:46.593 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/98/f5ff64ad7e0911529562f7fa7c6711]
Jun-01 15:28:46.613 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:46.613 [Task submitter] INFO  nextflow.Session - [86/899162] Submitted process > HMMSEARCH (batch_6)
Jun-01 15:28:53.752 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/86/899162d3b42f1b8c98456993f0b42a]
Jun-01 15:28:53.784 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:28:53.784 [Task submitter] INFO  nextflow.Session - [89/218585] Submitted process > HMMSEARCH (batch_18)
Jun-01 15:29:00.105 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/89/218585bc9bfedd448412a24fcec5d1]
Jun-01 15:29:00.140 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:29:00.140 [Task submitter] INFO  nextflow.Session - [c0/94a7b4] Submitted process > HMMSEARCH (batch_44)
Jun-01 15:29:06.979 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/94a7b496111c21c69ae95729749444]
Jun-01 15:29:06.997 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:29:06.997 [Task submitter] INFO  nextflow.Session - [b7/c8a97c] Submitted process > HMMSEARCH (batch_7)
Jun-01 15:29:13.640 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b7/c8a97c0885022d7806e424bf1272d6]
Jun-01 15:29:13.652 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:29:13.653 [Task submitter] INFO  nextflow.Session - [45/e51367] Submitted process > HMMSEARCH (batch_46)
Jun-01 15:29:20.061 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/45/e51367c1bdc872f713028719020bbf]
Jun-01 15:29:20.078 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:29:20.079 [Task submitter] INFO  nextflow.Session - [a8/053b34] Submitted process > HMMSEARCH (batch_47)
Jun-01 15:29:29.693 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a8/053b34946f63d0e7d9f5254d87bbdc]
Jun-01 15:29:29.717 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:29:29.717 [Task submitter] INFO  nextflow.Session - [0f/dd68fb] Submitted process > HMMSEARCH (batch_48)
Jun-01 15:29:42.392 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0f/dd68fbcdb2185eb2488e6a77975701]
Jun-01 15:29:42.443 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:29:42.443 [Task submitter] INFO  nextflow.Session - [70/8dc270] Submitted process > HMMSEARCH (batch_53)
Jun-01 15:29:55.107 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 2 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/abe5b5955aeba6e4f5d8a4dad67e3d]
~> TaskHandler[id: 55; name: HMMSEARCH (batch_53); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/8dc270c8fd606bf063094c5acc0505]
Jun-01 15:29:57.484 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/8dc270c8fd606bf063094c5acc0505]
Jun-01 15:29:57.515 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:29:57.515 [Task submitter] INFO  nextflow.Session - [97/bd6406] Submitted process > HMMSEARCH (batch_57)
Jun-01 15:30:03.526 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 65 -- tasks to be submitted are shown below
~> TaskHandler[id: 54; name: HMMSEARCH (batch_52); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5b/8e54813c70f891fdd7ae569e2b1e3b]
~> TaskHandler[id: 43; name: HMMSEARCH (batch_41); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ff/6a0b02c365d87fb7c7bff335202241]
~> TaskHandler[id: 57; name: HMMSEARCH (batch_55); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/38ee4afc276c0ce8edf32ef4b4daa6]
~> TaskHandler[id: 42; name: HMMSEARCH (batch_40); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/43/959a2165d6246efb3c9f80dc0bd399]
~> TaskHandler[id: 52; name: HMMSEARCH (batch_50); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3e/e61086fe7283643a512e5d9ed56705]
~> TaskHandler[id: 63; name: HMMSEARCH (batch_61); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/36/73cf8efe339c8b14cf31686f256fa5]
~> TaskHandler[id: 65; name: HMMSEARCH (batch_63); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3f/4cc919a067b3b84de66ca7036c4b04]
~> TaskHandler[id: 51; name: HMMSEARCH (batch_49); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9b/41591fcd0b20e9e313c99ec552f370]
~> TaskHandler[id: 44; name: HMMSEARCH (batch_42); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/a6301fb32baa4840ca2c6bb7cf2dc0]
~> TaskHandler[id: 47; name: HMMSEARCH (batch_45); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/d0afcafdee3d560de1a53474be35a2]
.. remaining tasks omitted.
Jun-01 15:30:17.719 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/bd640692ab6638d6deeb49135747ba]
Jun-01 15:30:17.738 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:30:17.739 [Task submitter] INFO  nextflow.Session - [5b/8e5481] Submitted process > HMMSEARCH (batch_52)
Jun-01 15:30:31.799 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5b/8e54813c70f891fdd7ae569e2b1e3b]
Jun-01 15:30:31.858 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:30:31.859 [Task submitter] INFO  nextflow.Session - [ff/6a0b02] Submitted process > HMMSEARCH (batch_41)
Jun-01 15:30:41.970 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ff/6a0b02c365d87fb7c7bff335202241]
Jun-01 15:30:41.984 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:30:41.984 [Task submitter] INFO  nextflow.Session - [c6/38ee4a] Submitted process > HMMSEARCH (batch_55)
Jun-01 15:30:51.232 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/38ee4afc276c0ce8edf32ef4b4daa6]
Jun-01 15:30:51.245 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:30:51.245 [Task submitter] INFO  nextflow.Session - [43/959a21] Submitted process > HMMSEARCH (batch_40)
Jun-01 15:31:01.147 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/43/959a2165d6246efb3c9f80dc0bd399]
Jun-01 15:31:01.179 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:31:01.180 [Task submitter] INFO  nextflow.Session - [3e/e61086] Submitted process > HMMSEARCH (batch_50)
Jun-01 15:31:11.674 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3e/e61086fe7283643a512e5d9ed56705]
Jun-01 15:31:11.697 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:31:11.697 [Task submitter] INFO  nextflow.Session - [36/73cf8e] Submitted process > HMMSEARCH (batch_61)
Jun-01 15:31:20.378 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/36/73cf8efe339c8b14cf31686f256fa5]
Jun-01 15:31:20.391 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:31:20.391 [Task submitter] INFO  nextflow.Session - [3f/4cc919] Submitted process > HMMSEARCH (batch_63)
Jun-01 15:31:29.219 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3f/4cc919a067b3b84de66ca7036c4b04]
Jun-01 15:31:29.242 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:31:29.243 [Task submitter] INFO  nextflow.Session - [9b/41591f] Submitted process > HMMSEARCH (batch_49)
Jun-01 15:31:38.474 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9b/41591fcd0b20e9e313c99ec552f370]
Jun-01 15:31:38.486 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:31:38.487 [Task submitter] INFO  nextflow.Session - [17/a6301f] Submitted process > HMMSEARCH (batch_42)
Jun-01 15:31:47.507 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/a6301fb32baa4840ca2c6bb7cf2dc0]
Jun-01 15:31:47.529 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:31:47.530 [Task submitter] INFO  nextflow.Session - [79/d0afca] Submitted process > HMMSEARCH (batch_45)
Jun-01 15:31:56.766 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/d0afcafdee3d560de1a53474be35a2]
Jun-01 15:31:56.786 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:31:56.787 [Task submitter] INFO  nextflow.Session - [17/d5becf] Submitted process > HMMSEARCH (batch_51)
Jun-01 15:32:05.653 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/d5becfdbc017356780ba7dd67117ef]
Jun-01 15:32:05.690 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:32:05.690 [Task submitter] INFO  nextflow.Session - [72/b5e5e5] Submitted process > HMMSEARCH (batch_39)
Jun-01 15:32:15.801 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/72/b5e5e544c6adac1f23dee64bea41d2]
Jun-01 15:32:15.814 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:32:15.814 [Task submitter] INFO  nextflow.Session - [9f/198e43] Submitted process > HMMSEARCH (batch_58)
Jun-01 15:32:25.207 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9f/198e43a6652072ab11fbca26953477]
Jun-01 15:32:25.289 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:32:25.289 [Task submitter] INFO  nextflow.Session - [fd/ee14d5] Submitted process > HMMSEARCH (batch_35)
Jun-01 15:32:34.507 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fd/ee14d525ca67bc521298bcc6c5bff3]
Jun-01 15:32:34.529 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:32:34.530 [Task submitter] INFO  nextflow.Session - [38/84765b] Submitted process > HMMSEARCH (batch_38)
Jun-01 15:32:43.275 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/38/84765b549f485ea66d2690956db5a1]
Jun-01 15:32:43.285 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:32:43.285 [Task submitter] INFO  nextflow.Session - [51/7a05b6] Submitted process > HMMSEARCH (batch_59)
Jun-01 15:32:51.273 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/51/7a05b6eef4ce6999d5841781d1e36a]
Jun-01 15:32:51.311 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:32:51.311 [Task submitter] INFO  nextflow.Session - [17/c9ab3f] Submitted process > HMMSEARCH (batch_54)
Jun-01 15:32:58.788 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/c9ab3f519ce25de37ce3e87e7f3399]
Jun-01 15:32:58.827 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:32:58.827 [Task submitter] INFO  nextflow.Session - [34/a09546] Submitted process > HMMSEARCH (batch_60)
Jun-01 15:33:06.706 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/34/a0954658ec86aaeedcb1ffaf9face1]
Jun-01 15:33:06.721 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:06.721 [Task submitter] INFO  nextflow.Session - [63/46f984] Submitted process > HMMSEARCH (batch_62)
Jun-01 15:33:14.301 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/46f9842ccc901347f06ffbcd9cdc1d]
Jun-01 15:33:14.330 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:14.330 [Task submitter] INFO  nextflow.Session - [a7/305a1d] Submitted process > HMMSEARCH (batch_56)
Jun-01 15:33:22.727 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a7/305a1d02945b38e4b3d78f5912247e]
Jun-01 15:33:22.740 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:22.740 [Task submitter] INFO  nextflow.Session - [7f/1cad12] Submitted process > HMMSEARCH (batch_43)
Jun-01 15:33:30.340 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/1cad1271db6dc279c4291cf3ab48e9]
Jun-01 15:33:30.360 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:30.360 [Task submitter] INFO  nextflow.Session - [a2/62edad] Submitted process > HMMSEARCH (batch_36)
Jun-01 15:33:37.582 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a2/62edad45cb19b68e1ac54cba360569]
Jun-01 15:33:37.596 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:37.596 [Task submitter] INFO  nextflow.Session - [5f/8fffca] Submitted process > HMMSEARCH (batch_37)
Jun-01 15:33:44.836 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/8fffcaff649be995040e621d61dafe]
Jun-01 15:33:44.851 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:44.851 [Task submitter] INFO  nextflow.Session - [ca/c7e6a8] Submitted process > HMMSEARCH (batch_66)
Jun-01 15:33:45.822 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ca/c7e6a8fb8a794739d97913efd1ba05]
Jun-01 15:33:45.845 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:45.845 [Task submitter] INFO  nextflow.Session - [62/fe1f64] Submitted process > HMMSEARCH (batch_65)
Jun-01 15:33:53.831 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/62/fe1f6470a9cc7a766d32403d099549]
Jun-01 15:33:53.841 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:33:53.841 [Task submitter] INFO  nextflow.Session - [1b/adc9e1] Submitted process > HMMSEARCH (batch_64)
Jun-01 15:34:00.137 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1b/adc9e14366b11b6535a9a3961ccb8b]
Jun-01 15:34:00.151 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.151 [Task submitter] INFO  nextflow.Session - [7d/80a28c] Submitted process > PROCESS_HITS (batch_10)
Jun-01 15:34:00.172 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.172 [Task submitter] INFO  nextflow.Session - [b1/5cd7e5] Submitted process > PROCESS_HITS (batch_29)
Jun-01 15:34:00.184 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.185 [Task submitter] INFO  nextflow.Session - [63/b381c1] Submitted process > PROCESS_HITS (batch_21)
Jun-01 15:34:00.205 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.206 [Task submitter] INFO  nextflow.Session - [52/ee9aa8] Submitted process > PROCESS_HITS (batch_16)
Jun-01 15:34:00.217 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.218 [Task submitter] INFO  nextflow.Session - [4f/2cb2aa] Submitted process > PROCESS_HITS (batch_4)
Jun-01 15:34:00.228 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.228 [Task submitter] INFO  nextflow.Session - [a4/568dd2] Submitted process > PROCESS_HITS (batch_25)
Jun-01 15:34:00.238 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.239 [Task submitter] INFO  nextflow.Session - [be/2ccce7] Submitted process > PROCESS_HITS (batch_24)
Jun-01 15:34:00.249 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:00.250 [Task submitter] INFO  nextflow.Session - [ef/2d493b] Submitted process > PROCESS_HITS (batch_13)
Jun-01 15:34:12.626 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/5cd7e5bbc36b25b38928dfca8be4f9]
Jun-01 15:34:12.640 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:12.640 [Task submitter] INFO  nextflow.Session - [60/97f52c] Submitted process > PROCESS_HITS (batch_32)
Jun-01 15:34:12.719 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/ee9aa8e3aee6422c92e2ad926a4f83]
Jun-01 15:34:12.735 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:12.736 [Task submitter] INFO  nextflow.Session - [1c/153bb6] Submitted process > PROCESS_HITS (batch_5)
Jun-01 15:34:12.826 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/be/2ccce7059dd7323bfc376adab9f9a5]
Jun-01 15:34:12.846 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a4/568dd206737ffce36f8fb4fad307e1]
Jun-01 15:34:12.850 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:12.850 [Task submitter] INFO  nextflow.Session - [58/c3e4b8] Submitted process > PROCESS_HITS (batch_15)
Jun-01 15:34:12.871 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:12.871 [Task submitter] INFO  nextflow.Session - [84/cbd0ef] Submitted process > PROCESS_HITS (batch_27)
Jun-01 15:34:12.896 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/2d493bf88c15893ae1517fa911b627]
Jun-01 15:34:12.942 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:12.943 [Task submitter] INFO  nextflow.Session - [83/35d28e] Submitted process > PROCESS_HITS (batch_33)
Jun-01 15:34:13.147 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4f/2cb2aa28aa04ec6a8a879f29ae4b22]
Jun-01 15:34:13.170 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:13.170 [Task submitter] INFO  nextflow.Session - [2e/6a8031] Submitted process > PROCESS_HITS (batch_14)
Jun-01 15:34:13.193 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/b381c18fc05a1d59cb6ee51548ccb1]
Jun-01 15:34:13.204 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7d/80a28ce8a22b226333e384e5d01eb3]
Jun-01 15:34:13.221 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:13.235 [Task submitter] INFO  nextflow.Session - [a8/372e00] Submitted process > PROCESS_HITS (batch_8)
Jun-01 15:34:13.253 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:13.253 [Task submitter] INFO  nextflow.Session - [84/18b135] Submitted process > PROCESS_HITS (batch_12)
Jun-01 15:34:26.316 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/153bb6eb5a3a900dec78b278d1a6a4]
Jun-01 15:34:26.332 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:26.332 [Task submitter] INFO  nextflow.Session - [57/7f18cc] Submitted process > PROCESS_HITS (batch_2)
Jun-01 15:34:26.449 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/58/c3e4b82f37aa215d2422da4b5a791f]
Jun-01 15:34:26.476 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:26.477 [Task submitter] INFO  nextflow.Session - [6a/6bcebc] Submitted process > PROCESS_HITS (batch_20)
Jun-01 15:34:26.479 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/cbd0efe2305c037f4d592ecf8307a4]
Jun-01 15:34:26.499 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:26.500 [Task submitter] INFO  nextflow.Session - [b6/1aca0b] Submitted process > PROCESS_HITS (batch_34)
Jun-01 15:34:26.521 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/60/97f52c2d69bba63f265d637c8028d7]
Jun-01 15:34:26.522 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/83/35d28e6f3e8fc2a5b3642330e1c3e8]
Jun-01 15:34:26.541 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:26.541 [Task submitter] INFO  nextflow.Session - [63/ccbf51] Submitted process > PROCESS_HITS (batch_23)
Jun-01 15:34:26.564 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:26.564 [Task submitter] INFO  nextflow.Session - [48/145852] Submitted process > PROCESS_HITS (batch_11)
Jun-01 15:34:26.879 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2e/6a8031ffbe69e66d946203646671f7]
Jun-01 15:34:26.894 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:26.894 [Task submitter] INFO  nextflow.Session - [a5/f14d2d] Submitted process > PROCESS_HITS (batch_17)
Jun-01 15:34:27.189 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a8/372e008bfd325557725680172a21c0]
Jun-01 15:34:27.201 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:27.201 [Task submitter] INFO  nextflow.Session - [21/4e1c3f] Submitted process > PROCESS_HITS (batch_9)
Jun-01 15:34:27.560 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/18b1356dbd2e1025f204058270a957]
Jun-01 15:34:27.589 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:27.589 [Task submitter] INFO  nextflow.Session - [5a/3f5f5d] Submitted process > PROCESS_HITS (batch_30)
Jun-01 15:34:39.672 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/1aca0b3575f94691c5014825781f0a]
Jun-01 15:34:39.694 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:39.694 [Task submitter] INFO  nextflow.Session - [16/e23c3e] Submitted process > PROCESS_HITS (batch_26)
Jun-01 15:34:39.796 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/7f18ccecf2304e360a9323a160873d]
Jun-01 15:34:39.808 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:39.808 [Task submitter] INFO  nextflow.Session - [87/dc7aab] Submitted process > PROCESS_HITS (batch_22)
Jun-01 15:34:39.835 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/6bcebcaa2384f0ddf884e6a1386a59]
Jun-01 15:34:39.849 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:39.849 [Task submitter] INFO  nextflow.Session - [d9/579f21] Submitted process > PROCESS_HITS (batch_1)
Jun-01 15:34:39.966 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/48/145852d58018654bffe851729febae]
Jun-01 15:34:39.992 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:39.992 [Task submitter] INFO  nextflow.Session - [ac/8cec83] Submitted process > PROCESS_HITS (batch_19)
Jun-01 15:34:40.072 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/f14d2d96fce561db4cadb145821c4f]
Jun-01 15:34:40.089 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:40.089 [Task submitter] INFO  nextflow.Session - [7d/dca0db] Submitted process > PROCESS_HITS (batch_28)
Jun-01 15:34:40.241 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/ccbf51e18192880a25a393b98cc504]
Jun-01 15:34:40.269 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:40.269 [Task submitter] INFO  nextflow.Session - [a9/4471b7] Submitted process > PROCESS_HITS (batch_31)
Jun-01 15:34:40.675 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/21/4e1c3f5a6708ffdb283a759bc035f6]
Jun-01 15:34:40.697 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:40.697 [Task submitter] INFO  nextflow.Session - [ef/ff2ed8] Submitted process > PROCESS_HITS (batch_3)
Jun-01 15:34:40.713 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/3f5f5df6f34f26723c7e0315677ebe]
Jun-01 15:34:40.734 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:40.735 [Task submitter] INFO  nextflow.Session - [fe/fd02a3] Submitted process > PROCESS_HITS (batch_6)
Jun-01 15:34:52.256 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/e23c3eab8d998e6597c5c100c8f24e]
Jun-01 15:34:52.268 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:52.268 [Task submitter] INFO  nextflow.Session - [80/876d45] Submitted process > PROCESS_HITS (batch_18)
Jun-01 15:34:52.301 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7d/dca0db3bcef3de813f8e0654cacffc]
Jun-01 15:34:52.322 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:52.323 [Task submitter] INFO  nextflow.Session - [5e/f622be] Submitted process > PROCESS_HITS (batch_44)
Jun-01 15:34:52.364 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/87/dc7aabeb3fe4d2ec21cf21cfbb9976]
Jun-01 15:34:52.378 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:52.379 [Task submitter] INFO  nextflow.Session - [2c/45c2b3] Submitted process > PROCESS_HITS (batch_7)
Jun-01 15:34:52.512 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ac/8cec834d20205f98faae556b4ce093]
Jun-01 15:34:52.527 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:52.527 [Task submitter] INFO  nextflow.Session - [67/c11eb5] Submitted process > PROCESS_HITS (batch_46)
Jun-01 15:34:52.600 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/4471b78e820d37a1ff6263b9880693]
Jun-01 15:34:52.623 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:52.623 [Task submitter] INFO  nextflow.Session - [57/441476] Submitted process > PROCESS_HITS (batch_47)
Jun-01 15:34:52.760 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d9/579f21d17daff359b90532ee189103]
Jun-01 15:34:52.780 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:52.780 [Task submitter] INFO  nextflow.Session - [72/c95da1] Submitted process > PROCESS_HITS (batch_48)
Jun-01 15:34:52.933 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/ff2ed80b4124e7f540980a6fb5cdda]
Jun-01 15:34:52.954 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:52.955 [Task submitter] INFO  nextflow.Session - [ad/458374] Submitted process > PROCESS_HITS (batch_53)
Jun-01 15:34:53.178 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fe/fd02a389b1b4d752f67798eccc2747]
Jun-01 15:34:53.206 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:34:53.206 [Task submitter] INFO  nextflow.Session - [27/fd2821] Submitted process > PROCESS_HITS (batch_57)
Jun-01 15:34:55.180 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 9 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/abe5b5955aeba6e4f5d8a4dad67e3d]
~> TaskHandler[id: 101; name: PROCESS_HITS (batch_18); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/80/876d45f42d28baf3fe49de55a56fab]
~> TaskHandler[id: 102; name: PROCESS_HITS (batch_44); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/f622be2b03f6c67389b5cfff6f4c9b]
~> TaskHandler[id: 103; name: PROCESS_HITS (batch_7); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2c/45c2b33c3dc1c177e60e8a916224ab]
~> TaskHandler[id: 104; name: PROCESS_HITS (batch_46); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/67/c11eb587d7d20f24b9b0044146f747]
~> TaskHandler[id: 105; name: PROCESS_HITS (batch_47); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/44147614bcbde975899d3b634d800d]
~> TaskHandler[id: 106; name: PROCESS_HITS (batch_48); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/72/c95da1fbf5d9150825f56caf823066]
~> TaskHandler[id: 107; name: PROCESS_HITS (batch_53); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ad/458374375e2334380ae973f5ff1b29]
~> TaskHandler[id: 108; name: PROCESS_HITS (batch_57); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/27/fd28214ca34621b0235359cae6bcea]
Jun-01 15:35:04.209 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 26 -- tasks to be submitted are shown below
~> TaskHandler[id: 109; name: PROCESS_HITS (batch_52); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/27/481c173099d56b8a564754e3e163da]
~> TaskHandler[id: 110; name: PROCESS_HITS (batch_41); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/5f7f30cec52e214d242b7c62a12a19]
~> TaskHandler[id: 111; name: PROCESS_HITS (batch_55); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/db/7e3a83c618beb09d8ac15e472efb89]
~> TaskHandler[id: 112; name: PROCESS_HITS (batch_40); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/632db430305b4bbff54e8a90adb79f]
~> TaskHandler[id: 113; name: PROCESS_HITS (batch_50); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/4e5df539c268db0c9a5a276b559427]
~> TaskHandler[id: 114; name: PROCESS_HITS (batch_61); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d1/2d73e8b66818bc937a6386ccc6dd09]
~> TaskHandler[id: 115; name: PROCESS_HITS (batch_63); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/a94b479e00783267b4a50cf3088761]
~> TaskHandler[id: 116; name: PROCESS_HITS (batch_49); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/4a1b561868720d4f85a80cd5a307d3]
~> TaskHandler[id: 117; name: PROCESS_HITS (batch_42); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/34a8a83384df287028cb2a40939bfd]
~> TaskHandler[id: 118; name: PROCESS_HITS (batch_45); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fe/63e1b18f348c608c11505106bba7e3]
.. remaining tasks omitted.
Jun-01 15:35:04.273 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/80/876d45f42d28baf3fe49de55a56fab]
Jun-01 15:35:04.292 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:04.292 [Task submitter] INFO  nextflow.Session - [27/481c17] Submitted process > PROCESS_HITS (batch_52)
Jun-01 15:35:04.398 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/44147614bcbde975899d3b634d800d]
Jun-01 15:35:04.414 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2c/45c2b33c3dc1c177e60e8a916224ab]
Jun-01 15:35:04.419 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:04.419 [Task submitter] INFO  nextflow.Session - [bd/5f7f30] Submitted process > PROCESS_HITS (batch_41)
Jun-01 15:35:04.435 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:04.435 [Task submitter] INFO  nextflow.Session - [db/7e3a83] Submitted process > PROCESS_HITS (batch_55)
Jun-01 15:35:04.454 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/f622be2b03f6c67389b5cfff6f4c9b]
Jun-01 15:35:04.479 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:04.479 [Task submitter] INFO  nextflow.Session - [17/632db4] Submitted process > PROCESS_HITS (batch_40)
Jun-01 15:35:04.719 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/27/fd28214ca34621b0235359cae6bcea]
Jun-01 15:35:04.736 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:04.736 [Task submitter] INFO  nextflow.Session - [e1/4e5df5] Submitted process > PROCESS_HITS (batch_50)
Jun-01 15:35:04.847 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ad/458374375e2334380ae973f5ff1b29]
Jun-01 15:35:04.860 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:04.860 [Task submitter] INFO  nextflow.Session - [d1/2d73e8] Submitted process > PROCESS_HITS (batch_61)
Jun-01 15:35:04.865 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/67/c11eb587d7d20f24b9b0044146f747]
Jun-01 15:35:04.889 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:04.889 [Task submitter] INFO  nextflow.Session - [f5/a94b47] Submitted process > PROCESS_HITS (batch_63)
Jun-01 15:35:05.016 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/72/c95da1fbf5d9150825f56caf823066]
Jun-01 15:35:05.037 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:05.037 [Task submitter] INFO  nextflow.Session - [5a/4a1b56] Submitted process > PROCESS_HITS (batch_49)
Jun-01 15:35:16.539 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/5f7f30cec52e214d242b7c62a12a19]
Jun-01 15:35:16.559 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:16.559 [Task submitter] INFO  nextflow.Session - [2a/34a8a8] Submitted process > PROCESS_HITS (batch_42)
Jun-01 15:35:16.597 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/db/7e3a83c618beb09d8ac15e472efb89]
Jun-01 15:35:16.598 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/27/481c173099d56b8a564754e3e163da]
Jun-01 15:35:16.615 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:16.615 [Task submitter] INFO  nextflow.Session - [fe/63e1b1] Submitted process > PROCESS_HITS (batch_45)
Jun-01 15:35:16.632 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:16.632 [Task submitter] INFO  nextflow.Session - [6d/27c522] Submitted process > PROCESS_HITS (batch_51)
Jun-01 15:35:16.725 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/4a1b561868720d4f85a80cd5a307d3]
Jun-01 15:35:16.741 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:16.741 [Task submitter] INFO  nextflow.Session - [9b/378d5a] Submitted process > PROCESS_HITS (batch_39)
Jun-01 15:35:16.804 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d1/2d73e8b66818bc937a6386ccc6dd09]
Jun-01 15:35:16.843 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:16.843 [Task submitter] INFO  nextflow.Session - [05/75e886] Submitted process > PROCESS_HITS (batch_58)
Jun-01 15:35:16.927 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/4e5df539c268db0c9a5a276b559427]
Jun-01 15:35:16.932 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/a94b479e00783267b4a50cf3088761]
Jun-01 15:35:16.957 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:16.957 [Task submitter] INFO  nextflow.Session - [55/45db62] Submitted process > PROCESS_HITS (batch_35)
Jun-01 15:35:16.975 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:16.976 [Task submitter] INFO  nextflow.Session - [13/9f7ea4] Submitted process > PROCESS_HITS (batch_38)
Jun-01 15:35:17.271 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/632db430305b4bbff54e8a90adb79f]
Jun-01 15:35:17.286 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:17.286 [Task submitter] INFO  nextflow.Session - [4e/9339d6] Submitted process > PROCESS_HITS (batch_59)
Jun-01 15:35:27.937 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fe/63e1b18f348c608c11505106bba7e3]
Jun-01 15:35:27.959 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:27.960 [Task submitter] INFO  nextflow.Session - [d4/f5ef8d] Submitted process > PROCESS_HITS (batch_54)
Jun-01 15:35:28.307 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/55/45db6285874fbc7a95c34502951e27]
Jun-01 15:35:28.326 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:28.326 [Task submitter] INFO  nextflow.Session - [8b/17b2ed] Submitted process > PROCESS_HITS (batch_60)
Jun-01 15:35:28.420 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6d/27c5220ee245e969cad15fbdb835db]
Jun-01 15:35:28.432 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:28.433 [Task submitter] INFO  nextflow.Session - [40/a87dc2] Submitted process > PROCESS_HITS (batch_62)
Jun-01 15:35:28.460 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/34a8a83384df287028cb2a40939bfd]
Jun-01 15:35:28.491 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:28.492 [Task submitter] INFO  nextflow.Session - [62/31c16e] Submitted process > PROCESS_HITS (batch_56)
Jun-01 15:35:28.564 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/9f7ea49e5448ac9bc66b78d58b2e41]
Jun-01 15:35:28.585 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:28.585 [Task submitter] INFO  nextflow.Session - [2e/0f8c9f] Submitted process > PROCESS_HITS (batch_43)
Jun-01 15:35:28.653 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/05/75e886c003bbfa5a31d4e80d389350]
Jun-01 15:35:28.665 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:28.665 [Task submitter] INFO  nextflow.Session - [61/c6e77d] Submitted process > PROCESS_HITS (batch_36)
Jun-01 15:35:28.936 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9b/378d5a6e8014b50b0b7b887430c6b9]
Jun-01 15:35:28.949 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:28.949 [Task submitter] INFO  nextflow.Session - [9f/ac8e8e] Submitted process > PROCESS_HITS (batch_37)
Jun-01 15:35:29.427 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/9339d6f4775226a40ab2e383ee6bc5]
Jun-01 15:35:29.444 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:29.444 [Task submitter] INFO  nextflow.Session - [5f/17749a] Submitted process > PROCESS_HITS (batch_66)
Jun-01 15:35:31.239 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/17749a3e12d3bacfefa67be9cec392]
Jun-01 15:35:31.254 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:31.255 [Task submitter] INFO  nextflow.Session - [3f/bad1ce] Submitted process > PROCESS_HITS (batch_65)
Jun-01 15:35:41.563 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d4/f5ef8d36b2c7da5a428d1bbd7ef15f]
Jun-01 15:35:41.569 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8b/17b2ed8007019244ac058fcdcdeb7b]
Jun-01 15:35:41.578 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:35:41.578 [Task submitter] INFO  nextflow.Session - [a6/c4d1e9] Submitted process > PROCESS_HITS (batch_64)
Jun-01 15:35:41.836 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/62/31c16e5f09bc24e4c73661c73be85c]
Jun-01 15:35:41.928 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2e/0f8c9f0c9a3044e16f1a85f7d40a49]
Jun-01 15:35:42.118 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/a87dc2ce396f2559e7b23e843a01d1]
Jun-01 15:35:42.302 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/c6e77d95e867cff32499adb4d4b8d0]
Jun-01 15:35:42.520 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9f/ac8e8ed85eae09eacd1ea06c4b7dfa]
Jun-01 15:35:44.945 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3f/bad1ce138b4c32ff17810e8cbbafe2]
Jun-01 15:35:53.786 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a6/c4d1e9ae1a07c6372cf8e6d9c03b8c]
Jun-01 15:39:55.257 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/abe5b5955aeba6e4f5d8a4dad67e3d]
Jun-01 15:42:15.845 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: GTDBTK (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/abe5b5955aeba6e4f5d8a4dad67e3d]
Jun-01 15:44:14.075 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 15:44:14.075 [Task submitter] INFO  nextflow.Session - [5f/c000b5] Submitted process > CREATE_DATAFRAME (1)
Jun-01 15:44:55.273 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 15:49:55.318 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 15:54:55.366 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 15:59:55.412 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 16:04:55.449 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 16:09:55.503 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 16:14:55.543 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 16:17:45.218 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/c000b5ca05a9089d3db1fffc0a1020]
Jun-01 16:17:45.261 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 16:17:45.261 [Task submitter] INFO  nextflow.Session - [e6/f5db98] Submitted process > AUTOENCODER (1)
Jun-01 16:18:55.227 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 136; name: AUTOENCODER (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e6/f5db98021e00b8b36e9812c110f52a]
Jun-01 16:18:55.272 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 16:18:55.272 [Task submitter] INFO  nextflow.Session - [e6/a3fef0] Submitted process > MODEL_PREDICT (1)
Jun-01 16:18:57.269 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 137; name: MODEL_PREDICT (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e6/a3fef0df03151eaf9c8fc685e98cdd]
Jun-01 16:18:57.276 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-01 16:18:57.370 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-01 16:18:57.370 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-01 16:18:57.373 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jun-01 16:18:57.374 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jun-01 16:18:57.379 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=137; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=9h 41m 45s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=14; peakCpus=42; peakMemory=84 GB; ]
Jun-01 16:18:57.592 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-01 16:18:57.621 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-01 16:18:57.622 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
