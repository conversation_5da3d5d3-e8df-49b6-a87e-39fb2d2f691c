Jun-02 14:08:19.376 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
Jun-02 14:08:19.533 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
Jun-02 14:08:19.553 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
Jun-02 14:08:19.580 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-02 14:08:19.582 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-02 14:08:19.585 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-02 14:08:19.597 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-02 14:08:19.614 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-02 14:08:19.616 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-02 14:08:19.653 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
Jun-02 14:08:19.656 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@9f6e406] - activable => nextflow.secret.LocalSecretsProvider@9f6e406
Jun-02 14:08:19.668 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Jun-02 14:08:20.141 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-02 14:08:20.156 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [mad_kare] DSL2 - revision: e9019381d3
Jun-02 14:08:20.158 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-02 14:08:20.159 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-02 14:08:20.202 [main] DEBUG nextflow.Session - Session UUID: 43b0eee7-2ec5-432b-a656-4cdc525ae443
Jun-02 14:08:20.203 [main] DEBUG nextflow.Session - Run name: mad_kare
Jun-02 14:08:20.205 [main] DEBUG nextflow.Session - Executor pool size: 32
Jun-02 14:08:20.213 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-02 14:08:20.218 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-02 14:08:20.239 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (82.9 GB) - Swap: 8 GB (2.2 MB)
Jun-02 14:08:20.279 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
Jun-02 14:08:20.312 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-02 14:08:20.325 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-02 14:08:20.330 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-02 14:08:20.353 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-02 14:08:20.360 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
Jun-02 14:08:20.539 [main] DEBUG nextflow.Session - Session start
Jun-02 14:08:21.214 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-02 14:08:21.253 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

Jun-02 14:08:21.372 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
Jun-02 14:08:21.383 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:08:21.383 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:08:21.392 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-02 14:08:21.397 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
Jun-02 14:08:21.399 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-02 14:08:21.419 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
Jun-02 14:08:21.480 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
Jun-02 14:08:21.496 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:08:21.497 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:08:21.500 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
Jun-02 14:08:21.501 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
Jun-02 14:08:21.520 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
Jun-02 14:08:21.522 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:08:21.522 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:08:21.524 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
Jun-02 14:08:21.528 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
Jun-02 14:08:21.550 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
Jun-02 14:08:21.551 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:08:21.552 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:08:21.554 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
Jun-02 14:08:21.563 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:08:21.563 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:08:21.567 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
Jun-02 14:08:21.575 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:08:21.575 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:08:21.579 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
Jun-02 14:08:21.583 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
Jun-02 14:08:21.586 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
Jun-02 14:08:21.592 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
Jun-02 14:08:21.593 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-02 14:08:21.594 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-02 14:08:21.596 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-02 14:08:21.597 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-02 14:08:21.600 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
Jun-02 14:08:21.602 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
Jun-02 14:08:21.604 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
Jun-02 14:08:21.605 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_1343ce0d68727479: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Jun-02 14:08:21.606 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-02 14:08:21.607 [main] DEBUG nextflow.Session - Session await
Jun-02 14:08:21.752 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:21.754 [Task submitter] INFO  nextflow.Session - [96/de42f7] Submitted process > PRODIGAL (1)
Jun-02 14:08:28.958 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/96/de42f7e0dd6eff4b3119443cd53132]
Jun-02 14:08:28.959 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-02 14:08:28.981 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-02 14:08:29.038 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:29.038 [Task submitter] INFO  nextflow.Session - [bc/5f7507] Submitted process > HMMSEARCH (batch_9)
Jun-02 14:08:29.059 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:29.060 [Task submitter] INFO  nextflow.Session - [2f/556c06] Submitted process > HMMSEARCH (batch_1)
Jun-02 14:08:36.718 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bc/5f75072d1da97b41523fcf2d747e72]
Jun-02 14:08:36.731 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:36.732 [Task submitter] INFO  nextflow.Session - [60/59a9cd] Submitted process > HMMSEARCH (batch_31)
Jun-02 14:08:36.833 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2f/556c066bb58a1451019005789eda88]
Jun-02 14:08:36.862 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:36.862 [Task submitter] INFO  nextflow.Session - [08/692965] Submitted process > HMMSEARCH (batch_24)
Jun-02 14:08:49.150 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/692965a7df172433f97385ae364b1b]
Jun-02 14:08:49.166 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:49.167 [Task submitter] INFO  nextflow.Session - [bf/fa94bf] Submitted process > HMMSEARCH (batch_27)
Jun-02 14:08:49.276 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/60/59a9cd6830e8d95b54cafb39f15406]
Jun-02 14:08:49.289 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:49.289 [Task submitter] INFO  nextflow.Session - [fc/414e56] Submitted process > HMMSEARCH (batch_10)
Jun-02 14:08:57.846 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/fa94bf60b290751257a67b34338e63]
Jun-02 14:08:57.868 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:57.869 [Task submitter] INFO  nextflow.Session - [75/ab8894] Submitted process > HMMSEARCH (batch_19)
Jun-02 14:08:57.895 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fc/414e56396b642fa38a4ff628417b9c]
Jun-02 14:08:57.909 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:08:57.910 [Task submitter] INFO  nextflow.Session - [d6/7d494d] Submitted process > HMMSEARCH (batch_3)
Jun-02 14:09:07.505 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d6/7d494d023371813dd6c927ab41a725]
Jun-02 14:09:07.520 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:07.521 [Task submitter] INFO  nextflow.Session - [fd/f255e7] Submitted process > HMMSEARCH (batch_14)
Jun-02 14:09:07.631 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/75/ab8894ec8e1a5c5d9727cb51bdfded]
Jun-02 14:09:07.648 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:07.649 [Task submitter] INFO  nextflow.Session - [73/e705c2] Submitted process > HMMSEARCH (batch_30)
Jun-02 14:09:13.950 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fd/f255e72380dcc2fc858a9abdde647c]
Jun-02 14:09:13.968 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:13.968 [Task submitter] INFO  nextflow.Session - [8a/b97f66] Submitted process > HMMSEARCH (batch_32)
Jun-02 14:09:13.990 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/73/e705c2d17928ddfa5812937dfe2deb]
Jun-02 14:09:14.010 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:14.010 [Task submitter] INFO  nextflow.Session - [7d/cc5f54] Submitted process > HMMSEARCH (batch_11)
Jun-02 14:09:21.274 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7d/cc5f542feedcef82acf2ffbd2315cb]
Jun-02 14:09:21.293 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:21.313 [Task submitter] INFO  nextflow.Session - [b9/5bc704] Submitted process > HMMSEARCH (batch_21)
Jun-02 14:09:21.472 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8a/b97f66f2a94f2f0cdfe99c47077cee]
Jun-02 14:09:21.489 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:21.490 [Task submitter] INFO  nextflow.Session - [31/4a99c5] Submitted process > HMMSEARCH (batch_2)
Jun-02 14:09:29.228 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/5bc70414907e33a4e8eace7e7bfe53]
Jun-02 14:09:29.240 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:29.241 [Task submitter] INFO  nextflow.Session - [98/3b8eab] Submitted process > HMMSEARCH (batch_18)
Jun-02 14:09:29.471 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/31/4a99c5c83d1382fe00970545092c5f]
Jun-02 14:09:29.492 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:29.493 [Task submitter] INFO  nextflow.Session - [fb/b52bb9] Submitted process > HMMSEARCH (batch_23)
Jun-02 14:09:35.867 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fb/b52bb9d326d4a98df4af329b8cec27]
Jun-02 14:09:35.878 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:35.878 [Task submitter] INFO  nextflow.Session - [c5/b1f7f1] Submitted process > HMMSEARCH (batch_28)
Jun-02 14:09:36.071 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/98/3b8eab8ba18315b483df84889ba065]
Jun-02 14:09:36.090 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:36.090 [Task submitter] INFO  nextflow.Session - [dc/d3a0bb] Submitted process > HMMSEARCH (batch_17)
Jun-02 14:09:41.899 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/b1f7f1b04302d352c6a64463af7ad2]
Jun-02 14:09:41.911 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:41.911 [Task submitter] INFO  nextflow.Session - [3e/042c83] Submitted process > HMMSEARCH (batch_8)
Jun-02 14:09:42.154 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dc/d3a0bbc445291900c83844c87a2d9b]
Jun-02 14:09:42.173 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:42.173 [Task submitter] INFO  nextflow.Session - [1f/872a51] Submitted process > HMMSEARCH (batch_5)
Jun-02 14:09:47.742 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3e/042c83e263cc116f6e88ee28be3b00]
Jun-02 14:09:47.759 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:47.760 [Task submitter] INFO  nextflow.Session - [eb/7a9ed9] Submitted process > HMMSEARCH (batch_29)
Jun-02 14:09:48.136 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1f/872a51ea06ee7c9cdb032faebfffc4]
Jun-02 14:09:48.147 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:48.147 [Task submitter] INFO  nextflow.Session - [da/a6668c] Submitted process > HMMSEARCH (batch_22)
Jun-02 14:09:54.850 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/eb/7a9ed9d00c6ba4ce7cb7cac52356e5]
Jun-02 14:09:54.862 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:54.863 [Task submitter] INFO  nextflow.Session - [19/5f30f0] Submitted process > HMMSEARCH (batch_6)
Jun-02 14:09:54.888 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/da/a6668ce567d3b1cbd91fc6b0e9c692]
Jun-02 14:09:54.899 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:09:54.900 [Task submitter] INFO  nextflow.Session - [63/09310e] Submitted process > HMMSEARCH (batch_13)
Jun-02 14:10:01.424 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/19/5f30f09c8a7cd87f60c61f797e35bf]
Jun-02 14:10:01.501 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:01.501 [Task submitter] INFO  nextflow.Session - [cb/3745ee] Submitted process > HMMSEARCH (batch_16)
Jun-02 14:10:01.573 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/09310e1637f6428cc4002691e5b94a]
Jun-02 14:10:01.592 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:01.593 [Task submitter] INFO  nextflow.Session - [f0/6e36d6] Submitted process > HMMSEARCH (batch_25)
Jun-02 14:10:08.687 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cb/3745eeb5b8c59a301473390a5f5ad5]
Jun-02 14:10:08.704 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:08.705 [Task submitter] INFO  nextflow.Session - [f4/02f8a9] Submitted process > HMMSEARCH (batch_12)
Jun-02 14:10:08.878 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f0/6e36d699e1a8d26f6aa9c2ccb47b78]
Jun-02 14:10:08.896 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:08.896 [Task submitter] INFO  nextflow.Session - [0a/a4985a] Submitted process > HMMSEARCH (batch_26)
Jun-02 14:10:17.254 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f4/02f8a9c03be563b3e96dd38242083d]
Jun-02 14:10:17.267 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:17.267 [Task submitter] INFO  nextflow.Session - [97/403203] Submitted process > HMMSEARCH (batch_15)
Jun-02 14:10:17.961 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/a4985a46b3f7ff8c804d5c1ea1c6ed]
Jun-02 14:10:17.983 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:17.984 [Task submitter] INFO  nextflow.Session - [66/b5b9ab] Submitted process > HMMSEARCH (batch_4)
Jun-02 14:10:24.233 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/40320365d7605a7ff313bcedecc6c2]
Jun-02 14:10:24.248 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:24.249 [Task submitter] INFO  nextflow.Session - [8f/c78998] Submitted process > HMMSEARCH (batch_7)
Jun-02 14:10:25.084 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/66/b5b9ab9a87f201cc0359039f8c2c7a]
Jun-02 14:10:25.102 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:25.103 [Task submitter] INFO  nextflow.Session - [b8/42da08] Submitted process > HMMSEARCH (batch_20)
Jun-02 14:10:31.857 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8f/c7899840df45970361020d58743b83]
Jun-02 14:10:31.924 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:31.931 [Task submitter] INFO  nextflow.Session - [63/282aa3] Submitted process > HMMSEARCH (batch_36)
Jun-02 14:10:33.175 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b8/42da081912c86204b9ef3ec6920c13]
Jun-02 14:10:33.192 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:33.192 [Task submitter] INFO  nextflow.Session - [88/92b4d0] Submitted process > HMMSEARCH (batch_37)
Jun-02 14:10:40.115 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/282aa3dd61c9e68ad4f24c494f1c1f]
Jun-02 14:10:40.127 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:40.128 [Task submitter] INFO  nextflow.Session - [f4/c92c57] Submitted process > HMMSEARCH (batch_44)
Jun-02 14:10:41.469 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/88/92b4d02bfdc8eadd1ce29a45077121]
Jun-02 14:10:41.485 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:41.486 [Task submitter] INFO  nextflow.Session - [8e/48b245] Submitted process > HMMSEARCH (batch_33)
Jun-02 14:10:50.416 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f4/c92c5709c0ae9a14a53324f69665a9]
Jun-02 14:10:50.446 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:50.446 [Task submitter] INFO  nextflow.Session - [d0/2c8ce4] Submitted process > HMMSEARCH (batch_51)
Jun-02 14:10:53.639 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/48b245a676fffc509084fc86b59426]
Jun-02 14:10:53.672 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:53.672 [Task submitter] INFO  nextflow.Session - [9a/a2cfa2] Submitted process > HMMSEARCH (batch_38)
Jun-02 14:10:58.860 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d0/2c8ce4d841c0e0c098604a87eda602]
Jun-02 14:10:58.889 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:10:58.889 [Task submitter] INFO  nextflow.Session - [ad/fcdd63] Submitted process > HMMSEARCH (batch_42)
Jun-02 14:11:00.238 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9a/a2cfa2e7f8ee6489ed7e01050b9893]
Jun-02 14:11:00.255 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:00.256 [Task submitter] INFO  nextflow.Session - [b1/be8526] Submitted process > HMMSEARCH (batch_50)
Jun-02 14:11:06.691 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ad/fcdd63cc89073b98d4386e40e470cd]
Jun-02 14:11:06.702 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:06.702 [Task submitter] INFO  nextflow.Session - [ec/3bb11f] Submitted process > HMMSEARCH (batch_45)
Jun-02 14:11:07.723 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/be8526cf0d35cbbaf1cbb9f2aeb30a]
Jun-02 14:11:07.740 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:07.740 [Task submitter] INFO  nextflow.Session - [7a/4dc821] Submitted process > HMMSEARCH (batch_47)
Jun-02 14:11:14.539 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ec/3bb11ff693a2e6c9d28817f73a46d1]
Jun-02 14:11:14.560 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:14.561 [Task submitter] INFO  nextflow.Session - [7c/4f0c8a] Submitted process > HMMSEARCH (batch_52)
Jun-02 14:11:15.868 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7a/4dc8214cbe21791ad8caa2b0f9df4d]
Jun-02 14:11:15.895 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:15.896 [Task submitter] INFO  nextflow.Session - [c3/1426b7] Submitted process > HMMSEARCH (batch_39)
Jun-02 14:11:25.294 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7c/4f0c8a4f7c1316e447c008e8aaff01]
Jun-02 14:11:25.345 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:25.345 [Task submitter] INFO  nextflow.Session - [82/412c15] Submitted process > HMMSEARCH (batch_58)
Jun-02 14:11:28.162 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c3/1426b7bf3f97e83343753496c0914e]
Jun-02 14:11:28.181 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:28.183 [Task submitter] INFO  nextflow.Session - [e0/0c57e0] Submitted process > HMMSEARCH (batch_46)
Jun-02 14:11:40.379 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/82/412c153114ba89b3d3f99c6e3316e9]
Jun-02 14:11:40.393 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:40.393 [Task submitter] INFO  nextflow.Session - [d2/4be6e8] Submitted process > HMMSEARCH (batch_34)
Jun-02 14:11:41.707 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e0/0c57e06af0cb1d41e5654b9f81e507]
Jun-02 14:11:41.765 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:41.765 [Task submitter] INFO  nextflow.Session - [bd/9847cd] Submitted process > HMMSEARCH (batch_53)
Jun-02 14:11:47.671 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d2/4be6e8fe1aa465a9f33983cd212d70]
Jun-02 14:11:47.683 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:47.684 [Task submitter] INFO  nextflow.Session - [4b/48a780] Submitted process > HMMSEARCH (batch_48)
Jun-02 14:11:48.672 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/9847cdc295a5233dd155ffa2cac3f4]
Jun-02 14:11:48.689 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:48.689 [Task submitter] INFO  nextflow.Session - [76/2d91d2] Submitted process > HMMSEARCH (batch_41)
Jun-02 14:11:53.931 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4b/48a780739bf5fdaf25df82bbc9fb1b]
Jun-02 14:11:53.945 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:53.946 [Task submitter] INFO  nextflow.Session - [cc/20065f] Submitted process > HMMSEARCH (batch_49)
Jun-02 14:11:55.571 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/76/2d91d2e012a30f507eb9e8fbc483fe]
Jun-02 14:11:55.584 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:11:55.585 [Task submitter] INFO  nextflow.Session - [e1/e47e10] Submitted process > HMMSEARCH (batch_55)
Jun-02 14:12:02.140 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cc/20065feed53c3a3723d553426a1f93]
Jun-02 14:12:02.207 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:02.207 [Task submitter] INFO  nextflow.Session - [15/c5d447] Submitted process > HMMSEARCH (batch_43)
Jun-02 14:12:05.214 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/e47e10685b012afac88d62a96b108d]
Jun-02 14:12:05.253 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:05.254 [Task submitter] INFO  nextflow.Session - [e4/8d7153] Submitted process > HMMSEARCH (batch_56)
Jun-02 14:12:14.306 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/c5d4473437602e40a7a79869824af9]
Jun-02 14:12:14.319 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:14.320 [Task submitter] INFO  nextflow.Session - [18/96ac7e] Submitted process > HMMSEARCH (batch_59)
Jun-02 14:12:17.185 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e4/8d7153613a7ec5344f01a6bdff6563]
Jun-02 14:12:17.214 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:17.215 [Task submitter] INFO  nextflow.Session - [ad/30a0f0] Submitted process > HMMSEARCH (batch_40)
Jun-02 14:12:21.638 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/18/96ac7ecdd47ce2ffd1b80f69c5bc3f]
Jun-02 14:12:21.649 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:21.649 [Task submitter] INFO  nextflow.Session - [7f/f59929] Submitted process > HMMSEARCH (batch_35)
Jun-02 14:12:23.181 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ad/30a0f05cd82abe7d07f37a90dffbf1]
Jun-02 14:12:23.192 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:23.192 [Task submitter] INFO  nextflow.Session - [91/3c2709] Submitted process > HMMSEARCH (batch_57)
Jun-02 14:12:27.400 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/f59929476bdf34011bef29a887e568]
Jun-02 14:12:27.419 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:27.419 [Task submitter] INFO  nextflow.Session - [3b/cc938c] Submitted process > HMMSEARCH (batch_60)
Jun-02 14:12:29.141 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/91/3c2709c6be735595219d67a95d1539]
Jun-02 14:12:29.152 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:29.152 [Task submitter] INFO  nextflow.Session - [5b/5623db] Submitted process > HMMSEARCH (batch_54)
Jun-02 14:12:34.261 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3b/cc938cbdfba1f7e4497f2052f4d00e]
Jun-02 14:12:34.278 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:34.279 [Task submitter] INFO  nextflow.Session - [80/648839] Submitted process > HMMSEARCH (batch_64)
Jun-02 14:12:36.150 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5b/5623dbec0565f84f9e19e04dcdf4f0]
Jun-02 14:12:36.162 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:36.162 [Task submitter] INFO  nextflow.Session - [05/0f33ba] Submitted process > HMMSEARCH (batch_62)
Jun-02 14:12:40.911 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/80/648839c7a33750b520f3e2dfae62c9]
Jun-02 14:12:40.952 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:40.952 [Task submitter] INFO  nextflow.Session - [ef/a150d3] Submitted process > HMMSEARCH (batch_65)
Jun-02 14:12:42.814 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/05/0f33baa0a4800b65fdf297e7327b26]
Jun-02 14:12:42.832 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:42.832 [Task submitter] INFO  nextflow.Session - [51/566f03] Submitted process > HMMSEARCH (batch_61)
Jun-02 14:12:48.136 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/a150d3f3bfc6800d0af9bb659c4300]
Jun-02 14:12:48.147 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:48.148 [Task submitter] INFO  nextflow.Session - [95/550dae] Submitted process > HMMSEARCH (batch_66)
Jun-02 14:12:49.174 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/95/550daec986abb34dc5ccfe87e05980]
Jun-02 14:12:49.212 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:49.213 [Task submitter] INFO  nextflow.Session - [52/c47e07] Submitted process > HMMSEARCH (batch_63)
Jun-02 14:12:51.936 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/51/566f034397d9dc9ee1763d2de632de]
Jun-02 14:12:51.950 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:51.950 [Task submitter] INFO  nextflow.Session - [ea/d057fb] Submitted process > PROCESS_HITS (batch_9)
Jun-02 14:12:51.972 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:51.972 [Task submitter] INFO  nextflow.Session - [0a/571d77] Submitted process > PROCESS_HITS (batch_1)
Jun-02 14:12:51.984 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:51.984 [Task submitter] INFO  nextflow.Session - [af/c86af1] Submitted process > PROCESS_HITS (batch_24)
Jun-02 14:12:52.001 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:52.001 [Task submitter] INFO  nextflow.Session - [97/46a726] Submitted process > PROCESS_HITS (batch_31)
Jun-02 14:12:52.029 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:52.029 [Task submitter] INFO  nextflow.Session - [e6/ce96eb] Submitted process > PROCESS_HITS (batch_27)
Jun-02 14:12:52.051 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:52.051 [Task submitter] INFO  nextflow.Session - [01/1366cc] Submitted process > PROCESS_HITS (batch_10)
Jun-02 14:12:52.064 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:52.064 [Task submitter] INFO  nextflow.Session - [48/419059] Submitted process > PROCESS_HITS (batch_3)
Jun-02 14:12:52.081 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:52.081 [Task submitter] INFO  nextflow.Session - [49/40a7ad] Submitted process > PROCESS_HITS (batch_19)
Jun-02 14:12:56.416 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/c47e075cb4fe56480bb41660a0e734]
Jun-02 14:12:56.426 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.426 [Task submitter] INFO  nextflow.Session - [9d/4e0dea] Submitted process > PROCESS_HITS (batch_14)
Jun-02 14:12:56.442 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.443 [Task submitter] INFO  nextflow.Session - [f6/018463] Submitted process > PROCESS_HITS (batch_30)
Jun-02 14:12:56.454 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.454 [Task submitter] INFO  nextflow.Session - [2d/4479fe] Submitted process > PROCESS_HITS (batch_11)
Jun-02 14:12:56.466 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.466 [Task submitter] INFO  nextflow.Session - [7a/1b23e9] Submitted process > PROCESS_HITS (batch_32)
Jun-02 14:12:56.492 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.492 [Task submitter] INFO  nextflow.Session - [40/ce3770] Submitted process > PROCESS_HITS (batch_21)
Jun-02 14:12:56.503 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.503 [Task submitter] INFO  nextflow.Session - [07/3a232b] Submitted process > PROCESS_HITS (batch_2)
Jun-02 14:12:56.520 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.520 [Task submitter] INFO  nextflow.Session - [51/06c775] Submitted process > PROCESS_HITS (batch_23)
Jun-02 14:12:56.532 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:12:56.532 [Task submitter] INFO  nextflow.Session - [81/fce428] Submitted process > PROCESS_HITS (batch_18)
Jun-02 14:13:05.775 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e6/ce96eb1ac00bdd035286cd2e3b18d0]
Jun-02 14:13:05.795 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:05.796 [Task submitter] INFO  nextflow.Session - [79/ca8bf4] Submitted process > PROCESS_HITS (batch_28)
Jun-02 14:13:06.052 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/01/1366cc6aa2bcf480420dbe9b1e15dc]
Jun-02 14:13:06.071 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:06.071 [Task submitter] INFO  nextflow.Session - [df/4e54f0] Submitted process > PROCESS_HITS (batch_17)
Jun-02 14:13:06.348 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/c86af1927babe9c372ee3218294289]
Jun-02 14:13:06.393 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:06.393 [Task submitter] INFO  nextflow.Session - [16/f99c63] Submitted process > PROCESS_HITS (batch_8)
Jun-02 14:13:06.633 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/48/419059b71f46c94c6cdcc1b7a4996f]
Jun-02 14:13:06.673 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:06.674 [Task submitter] INFO  nextflow.Session - [a2/3c9dfa] Submitted process > PROCESS_HITS (batch_5)
Jun-02 14:13:07.141 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/46a726069e72a8aa7562b14c7f2f1f]
Jun-02 14:13:07.180 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:07.180 [Task submitter] INFO  nextflow.Session - [99/f770ac] Submitted process > PROCESS_HITS (batch_29)
Jun-02 14:13:07.282 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/571d77810db2f306ed06a5039159e6]
Jun-02 14:13:07.303 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ea/d057fbe6c1c95f2d84a1b50cd65324]
Jun-02 14:13:07.323 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:07.323 [Task submitter] INFO  nextflow.Session - [2f/abc80b] Submitted process > PROCESS_HITS (batch_22)
Jun-02 14:13:07.354 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:07.354 [Task submitter] INFO  nextflow.Session - [e2/e2c57b] Submitted process > PROCESS_HITS (batch_6)
Jun-02 14:13:07.477 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/49/40a7ad2ad67e776520d44fafb8d5e9]
Jun-02 14:13:07.499 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:07.499 [Task submitter] INFO  nextflow.Session - [2c/b2658c] Submitted process > PROCESS_HITS (batch_13)
Jun-02 14:13:13.599 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f6/018463537d456ccc64b15046b9ef38]
Jun-02 14:13:13.621 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:13.621 [Task submitter] INFO  nextflow.Session - [13/0bc870] Submitted process > PROCESS_HITS (batch_16)
Jun-02 14:13:13.883 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9d/4e0dea9d14ea6f46377b1cc36c0525]
Jun-02 14:13:13.906 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:13.907 [Task submitter] INFO  nextflow.Session - [6b/c6a06b] Submitted process > PROCESS_HITS (batch_25)
Jun-02 14:13:14.119 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2d/4479feb71342995c23fbbcd308f74f]
Jun-02 14:13:14.133 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/ce3770574847c1d73d2f95ca2a366e]
Jun-02 14:13:14.135 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:14.136 [Task submitter] INFO  nextflow.Session - [24/79d417] Submitted process > PROCESS_HITS (batch_12)
Jun-02 14:13:14.154 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/51/06c77550dd77e1aee9ad1523a7a139]
Jun-02 14:13:14.162 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:14.162 [Task submitter] INFO  nextflow.Session - [6d/f75724] Submitted process > PROCESS_HITS (batch_26)
Jun-02 14:13:14.181 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:14.181 [Task submitter] INFO  nextflow.Session - [53/0fc7bc] Submitted process > PROCESS_HITS (batch_15)
Jun-02 14:13:14.362 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7a/1b23e9db8cd882aa53e9c2ec83bba5]
Jun-02 14:13:14.364 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/07/3a232bd438ec734f26d9a8a142d618]
Jun-02 14:13:14.368 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/81/fce4287d71477574ecd166f27c246d]
Jun-02 14:13:14.396 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:14.396 [Task submitter] INFO  nextflow.Session - [ba/6f040e] Submitted process > PROCESS_HITS (batch_4)
Jun-02 14:13:14.411 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:14.411 [Task submitter] INFO  nextflow.Session - [c1/346e17] Submitted process > PROCESS_HITS (batch_7)
Jun-02 14:13:14.424 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:14.424 [Task submitter] INFO  nextflow.Session - [5e/5736f3] Submitted process > PROCESS_HITS (batch_20)
Jun-02 14:13:21.576 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 16 -- submitted tasks are shown below
~> TaskHandler[id: 84; name: PROCESS_HITS (batch_28); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/ca8bf417b6044f6588cdbcb4ecb8df]
~> TaskHandler[id: 85; name: PROCESS_HITS (batch_17); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/4e54f0d1001fb1066da9705e83de81]
~> TaskHandler[id: 86; name: PROCESS_HITS (batch_8); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/f99c6327147e1123c5146052dc4d35]
~> TaskHandler[id: 87; name: PROCESS_HITS (batch_5); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a2/3c9dfa93e479e835530eb8df061ed6]
~> TaskHandler[id: 88; name: PROCESS_HITS (batch_29); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/99/f770aca19da1ef9d115c213858274e]
~> TaskHandler[id: 89; name: PROCESS_HITS (batch_22); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2f/abc80b59adc33def9b22620fb4cd50]
~> TaskHandler[id: 90; name: PROCESS_HITS (batch_6); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e2/e2c57b938a7009b645cd494349d675]
~> TaskHandler[id: 91; name: PROCESS_HITS (batch_13); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2c/b2658c93aab0703dcb393244baa146]
~> TaskHandler[id: 92; name: PROCESS_HITS (batch_16); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/0bc8704ea0b49987d3f72558a5c8ce]
~> TaskHandler[id: 93; name: PROCESS_HITS (batch_25); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/c6a06b390bf4ba1d765546b4fb828c]
.. remaining tasks omitted.
Jun-02 14:13:21.944 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/ca8bf417b6044f6588cdbcb4ecb8df]
Jun-02 14:13:21.984 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:21.984 [Task submitter] INFO  nextflow.Session - [5a/82c732] Submitted process > PROCESS_HITS (batch_36)
Jun-02 14:13:23.779 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e2/e2c57b938a7009b645cd494349d675]
Jun-02 14:13:23.815 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:23.816 [Task submitter] INFO  nextflow.Session - [32/cd1335] Submitted process > PROCESS_HITS (batch_37)
Jun-02 14:13:23.951 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/99/f770aca19da1ef9d115c213858274e]
Jun-02 14:13:23.964 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2c/b2658c93aab0703dcb393244baa146]
Jun-02 14:13:23.981 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:23.988 [Task submitter] INFO  nextflow.Session - [ae/93a621] Submitted process > PROCESS_HITS (batch_44)
Jun-02 14:13:24.018 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/4e54f0d1001fb1066da9705e83de81]
Jun-02 14:13:24.043 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:24.043 [Task submitter] INFO  nextflow.Session - [63/720425] Submitted process > PROCESS_HITS (batch_33)
Jun-02 14:13:24.046 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a2/3c9dfa93e479e835530eb8df061ed6]
Jun-02 14:13:24.078 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:24.078 [Task submitter] INFO  nextflow.Session - [44/bddb28] Submitted process > PROCESS_HITS (batch_51)
Jun-02 14:13:24.100 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:24.101 [Task submitter] INFO  nextflow.Session - [8e/c8e121] Submitted process > PROCESS_HITS (batch_38)
Jun-02 14:13:24.136 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/f99c6327147e1123c5146052dc4d35]
Jun-02 14:13:24.187 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:24.187 [Task submitter] INFO  nextflow.Session - [e1/143cd2] Submitted process > PROCESS_HITS (batch_42)
Jun-02 14:13:24.435 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2f/abc80b59adc33def9b22620fb4cd50]
Jun-02 14:13:24.467 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:24.467 [Task submitter] INFO  nextflow.Session - [c4/a89848] Submitted process > PROCESS_HITS (batch_50)
Jun-02 14:13:29.469 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 26 -- tasks to be submitted are shown below
~> TaskHandler[id: 108; name: PROCESS_HITS (batch_45); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/d3a9e5be122cd1c6abe63544e26b27]
~> TaskHandler[id: 109; name: PROCESS_HITS (batch_47); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/78cbfcac346f286f633fff49629f00]
~> TaskHandler[id: 110; name: PROCESS_HITS (batch_52); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/b76f782c66d57e2dc975e3fc56f3ff]
~> TaskHandler[id: 111; name: PROCESS_HITS (batch_39); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/b28de8ec27390bf81307e54536a9ed]
~> TaskHandler[id: 112; name: PROCESS_HITS (batch_58); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/26142bde5ab1c1f5379060c678ba65]
~> TaskHandler[id: 113; name: PROCESS_HITS (batch_46); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/f9b6e204bb7fa9cfb50bf2b4c367bb]
~> TaskHandler[id: 114; name: PROCESS_HITS (batch_34); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/21/9b24cda6a3e14023170cc5221d8ab4]
~> TaskHandler[id: 115; name: PROCESS_HITS (batch_53); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/20f20370dae28326883d3d880d0a5d]
~> TaskHandler[id: 116; name: PROCESS_HITS (batch_48); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ab/fb1c342a6ebe05d3b34d6039d46a2a]
~> TaskHandler[id: 117; name: PROCESS_HITS (batch_41); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/55/7c1605f386ce653d31469643f3ea56]
.. remaining tasks omitted.
Jun-02 14:13:29.936 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/0bc8704ea0b49987d3f72558a5c8ce]
Jun-02 14:13:29.952 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:29.953 [Task submitter] INFO  nextflow.Session - [24/d3a9e5] Submitted process > PROCESS_HITS (batch_45)
Jun-02 14:13:30.451 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/79d417bb91a39a7a0d32ce2639d6a5]
Jun-02 14:13:30.467 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:30.468 [Task submitter] INFO  nextflow.Session - [c9/78cbfc] Submitted process > PROCESS_HITS (batch_47)
Jun-02 14:13:30.491 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c1/346e1728881dcd73dce7902b5111a3]
Jun-02 14:13:30.509 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/c6a06b390bf4ba1d765546b4fb828c]
Jun-02 14:13:30.510 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:30.510 [Task submitter] INFO  nextflow.Session - [5e/b76f78] Submitted process > PROCESS_HITS (batch_52)
Jun-02 14:13:30.534 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:30.534 [Task submitter] INFO  nextflow.Session - [a9/b28de8] Submitted process > PROCESS_HITS (batch_39)
Jun-02 14:13:30.569 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6d/f7572491fb6f95910262e2127ffc87]
Jun-02 14:13:30.570 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/53/0fc7bc0d6d44cc549c61c2f38460da]
Jun-02 14:13:30.570 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/5736f3073abab3ab39a5d380aec431]
Jun-02 14:13:30.609 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:30.609 [Task submitter] INFO  nextflow.Session - [f1/26142b] Submitted process > PROCESS_HITS (batch_58)
Jun-02 14:13:30.625 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:30.625 [Task submitter] INFO  nextflow.Session - [2b/f9b6e2] Submitted process > PROCESS_HITS (batch_46)
Jun-02 14:13:30.647 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:30.647 [Task submitter] INFO  nextflow.Session - [21/9b24cd] Submitted process > PROCESS_HITS (batch_34)
Jun-02 14:13:30.904 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/6f040e11d4c09999f005a185cdc5d1]
Jun-02 14:13:30.930 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:30.931 [Task submitter] INFO  nextflow.Session - [c4/20f203] Submitted process > PROCESS_HITS (batch_53)
Jun-02 14:13:40.070 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/82c732651028561fffbf97fb73e1f6]
Jun-02 14:13:40.089 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:40.089 [Task submitter] INFO  nextflow.Session - [ab/fb1c34] Submitted process > PROCESS_HITS (batch_48)
Jun-02 14:13:40.868 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/7204256dbf76da493b151e3c07e0a2]
Jun-02 14:13:40.882 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:40.882 [Task submitter] INFO  nextflow.Session - [55/7c1605] Submitted process > PROCESS_HITS (batch_41)
Jun-02 14:13:41.113 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ae/93a621fd25ad8b8b40b3a18fc70065]
Jun-02 14:13:41.125 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:41.125 [Task submitter] INFO  nextflow.Session - [d0/793997] Submitted process > PROCESS_HITS (batch_49)
Jun-02 14:13:41.255 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/32/cd13356928c09bb44b916b8f67adf4]
Jun-02 14:13:41.269 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:41.269 [Task submitter] INFO  nextflow.Session - [f7/4cf4cf] Submitted process > PROCESS_HITS (batch_55)
Jun-02 14:13:41.380 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/a898484a8be30f332f6ee522ad9c12]
Jun-02 14:13:41.394 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:41.394 [Task submitter] INFO  nextflow.Session - [dd/55f17b] Submitted process > PROCESS_HITS (batch_43)
Jun-02 14:13:41.429 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/44/bddb28917681b3959f588f39432e3e]
Jun-02 14:13:41.445 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:41.445 [Task submitter] INFO  nextflow.Session - [5a/9d1e6e] Submitted process > PROCESS_HITS (batch_56)
Jun-02 14:13:41.705 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/143cd220c6bca81f796534fdc1ac5c]
Jun-02 14:13:41.718 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/c8e121b0215254b74781808e847b0e]
Jun-02 14:13:41.720 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:41.721 [Task submitter] INFO  nextflow.Session - [2c/c226dc] Submitted process > PROCESS_HITS (batch_59)
Jun-02 14:13:41.742 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:41.743 [Task submitter] INFO  nextflow.Session - [5f/372d5e] Submitted process > PROCESS_HITS (batch_40)
Jun-02 14:13:46.796 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/d3a9e5be122cd1c6abe63544e26b27]
Jun-02 14:13:46.824 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:46.824 [Task submitter] INFO  nextflow.Session - [92/74e088] Submitted process > PROCESS_HITS (batch_35)
Jun-02 14:13:47.450 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/b28de8ec27390bf81307e54536a9ed]
Jun-02 14:13:47.471 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:47.471 [Task submitter] INFO  nextflow.Session - [70/fd522d] Submitted process > PROCESS_HITS (batch_57)
Jun-02 14:13:47.510 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/21/9b24cda6a3e14023170cc5221d8ab4]
Jun-02 14:13:47.532 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:47.532 [Task submitter] INFO  nextflow.Session - [ea/c21a23] Submitted process > PROCESS_HITS (batch_60)
Jun-02 14:13:47.859 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/b76f782c66d57e2dc975e3fc56f3ff]
Jun-02 14:13:47.881 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:47.881 [Task submitter] INFO  nextflow.Session - [e0/9e9874] Submitted process > PROCESS_HITS (batch_54)
Jun-02 14:13:48.333 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/20f20370dae28326883d3d880d0a5d]
Jun-02 14:13:48.375 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:48.375 [Task submitter] INFO  nextflow.Session - [a1/871640] Submitted process > PROCESS_HITS (batch_64)
Jun-02 14:13:48.407 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/78cbfcac346f286f633fff49629f00]
Jun-02 14:13:48.429 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:48.429 [Task submitter] INFO  nextflow.Session - [04/c22454] Submitted process > PROCESS_HITS (batch_62)
Jun-02 14:13:48.446 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/f9b6e204bb7fa9cfb50bf2b4c367bb]
Jun-02 14:13:48.449 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/26142bde5ab1c1f5379060c678ba65]
Jun-02 14:13:48.468 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:48.468 [Task submitter] INFO  nextflow.Session - [5c/f0a295] Submitted process > PROCESS_HITS (batch_65)
Jun-02 14:13:48.489 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:48.489 [Task submitter] INFO  nextflow.Session - [89/bf2cfd] Submitted process > PROCESS_HITS (batch_66)
Jun-02 14:13:50.139 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/89/bf2cfd2b1822b745c037fc1c8fc033]
Jun-02 14:13:50.151 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:50.151 [Task submitter] INFO  nextflow.Session - [3b/aa5bb7] Submitted process > PROCESS_HITS (batch_61)
Jun-02 14:13:53.866 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ab/fb1c342a6ebe05d3b34d6039d46a2a]
Jun-02 14:13:53.881 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:13:53.882 [Task submitter] INFO  nextflow.Session - [b9/0b562d] Submitted process > PROCESS_HITS (batch_63)
Jun-02 14:13:54.921 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/55/7c1605f386ce653d31469643f3ea56]
Jun-02 14:13:55.083 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f7/4cf4cf92e939d56be15732d5df7658]
Jun-02 14:13:55.264 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d0/793997a90c3bfb9900a7ab13d5d2a7]
Jun-02 14:13:55.294 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/9d1e6e614dcccf975bcc9475971900]
Jun-02 14:13:55.607 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dd/55f17bf1cd310234cd627863decc95]
Jun-02 14:13:55.636 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/372d5ed13ee23d83c4faa7a63b2014]
Jun-02 14:13:55.682 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2c/c226dcc1ceeff0bb4cb71c4c741cf2]
Jun-02 14:14:00.508 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/92/74e0881ececb0ecb185b3f420f76ba]
Jun-02 14:14:01.454 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/fd522d5642df30ffa129841f94a71d]
Jun-02 14:14:02.055 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ea/c21a2316b5061210fb0d227322766b]
Jun-02 14:14:02.198 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e0/9e987497fe12c3177682f881bd98c1]
Jun-02 14:14:02.617 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/04/c2245496214962a53c7070e84476ca]
Jun-02 14:14:02.631 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5c/f0a2957c416893daabd49b3f187923]
Jun-02 14:14:02.817 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/8716401401f200b33d016435b3f816]
Jun-02 14:14:04.270 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3b/aa5bb76a992fae04db0311398c6358]
Jun-02 14:14:09.796 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/0b562d743d4a6d0952a37b7cdd4183]
Jun-02 14:15:03.125 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:15:03.125 [Task submitter] INFO  nextflow.Session - [03/0381f8] Submitted process > CREATE_DATAFRAME (1)
Jun-02 14:18:21.621 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/0381f8a09c6476751ea7ede3acb5f2]
Jun-02 14:23:21.649 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/0381f8a09c6476751ea7ede3acb5f2]
Jun-02 14:28:21.683 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/0381f8a09c6476751ea7ede3acb5f2]
Jun-02 14:33:21.722 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/0381f8a09c6476751ea7ede3acb5f2]
Jun-02 14:38:21.778 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/0381f8a09c6476751ea7ede3acb5f2]
Jun-02 14:43:21.834 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/0381f8a09c6476751ea7ede3acb5f2]
Jun-02 14:44:11.664 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/0381f8a09c6476751ea7ede3acb5f2]
Jun-02 14:44:11.712 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:44:11.712 [Task submitter] INFO  nextflow.Session - [19/974667] Submitted process > AUTOENCODER (1)
Jun-02 14:45:19.744 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: AUTOENCODER (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/19/97466703b87049f0905078b440af5a]
Jun-02 14:45:19.804 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:45:19.804 [Task submitter] INFO  nextflow.Session - [91/d8c992] Submitted process > MODEL_PREDICT (1)
Jun-02 14:45:23.245 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 136; name: MODEL_PREDICT (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/91/d8c9929bf278febb414e2fb9dffba4]
Jun-02 14:45:23.248 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-02 14:45:23.345 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-02 14:45:23.345 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-02 14:45:23.362 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jun-02 14:45:23.372 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jun-02 14:45:23.379 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=136; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=4h 52m 18s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=20; peakCpus=40; peakMemory=80 GB; ]
Jun-02 14:45:23.531 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-02 14:45:23.574 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-02 14:45:23.574 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
