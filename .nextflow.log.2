Jun-02 12:11:21.944 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
Jun-02 12:11:22.074 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
Jun-02 12:11:22.094 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
Jun-02 12:11:22.119 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-02 12:11:22.121 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-02 12:11:22.123 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-02 12:11:22.135 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-02 12:11:22.152 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-02 12:11:22.155 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-02 12:11:22.179 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
Jun-02 12:11:22.182 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@9f6e406] - activable => nextflow.secret.LocalSecretsProvider@9f6e406
Jun-02 12:11:22.208 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Jun-02 12:11:22.705 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-02 12:11:22.721 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [loquacious_gilbert] DSL2 - revision: e9019381d3
Jun-02 12:11:22.723 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-02 12:11:22.725 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-02 12:11:22.776 [main] DEBUG nextflow.Session - Session UUID: 79c739a1-0c85-4382-9dcc-5ff76a52ad96
Jun-02 12:11:22.776 [main] DEBUG nextflow.Session - Run name: loquacious_gilbert
Jun-02 12:11:22.778 [main] DEBUG nextflow.Session - Executor pool size: 32
Jun-02 12:11:22.793 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-02 12:11:22.798 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-02 12:11:22.819 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (90.2 GB) - Swap: 8 GB (2.2 MB)
Jun-02 12:11:22.852 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
Jun-02 12:11:22.884 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-02 12:11:22.898 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-02 12:11:22.902 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-02 12:11:22.926 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-02 12:11:22.934 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
Jun-02 12:11:23.051 [main] DEBUG nextflow.Session - Session start
Jun-02 12:11:23.716 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-02 12:11:23.756 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

Jun-02 12:11:23.869 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
Jun-02 12:11:23.880 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 12:11:23.880 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 12:11:23.890 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-02 12:11:23.895 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
Jun-02 12:11:23.897 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-02 12:11:23.918 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
Jun-02 12:11:23.976 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
Jun-02 12:11:23.994 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 12:11:23.995 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 12:11:23.997 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
Jun-02 12:11:23.999 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
Jun-02 12:11:24.013 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
Jun-02 12:11:24.015 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 12:11:24.015 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 12:11:24.022 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
Jun-02 12:11:24.023 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
Jun-02 12:11:24.048 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
Jun-02 12:11:24.050 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 12:11:24.050 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 12:11:24.053 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
Jun-02 12:11:24.070 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 12:11:24.071 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 12:11:24.073 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
Jun-02 12:11:24.080 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 12:11:24.080 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 12:11:24.084 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
Jun-02 12:11:24.091 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
Jun-02 12:11:24.094 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
Jun-02 12:11:24.101 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
Jun-02 12:11:24.101 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-02 12:11:24.105 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-02 12:11:24.106 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-02 12:11:24.108 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-02 12:11:24.110 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
Jun-02 12:11:24.112 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
Jun-02 12:11:24.114 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
Jun-02 12:11:24.116 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_1343ce0d68727479: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Jun-02 12:11:24.116 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-02 12:11:24.118 [main] DEBUG nextflow.Session - Session await
Jun-02 12:11:24.242 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:24.244 [Task submitter] INFO  nextflow.Session - [c0/050294] Submitted process > PRODIGAL (1)
Jun-02 12:11:31.397 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/05029401e4f3d87a3b970c261e8b06]
Jun-02 12:11:31.398 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-02 12:11:31.420 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-02 12:11:31.474 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:31.475 [Task submitter] INFO  nextflow.Session - [17/a15c57] Submitted process > HMMSEARCH (batch_14)
Jun-02 12:11:31.504 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:31.504 [Task submitter] INFO  nextflow.Session - [c9/85e862] Submitted process > HMMSEARCH (batch_8)
Jun-02 12:11:37.783 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/85e862df54945f7a0ae2db1f863554]
Jun-02 12:11:37.804 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:37.805 [Task submitter] INFO  nextflow.Session - [c6/c7f359] Submitted process > HMMSEARCH (batch_19)
Jun-02 12:11:37.906 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/a15c5759702a995e9a5b2bd8ec542a]
Jun-02 12:11:37.932 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:37.932 [Task submitter] INFO  nextflow.Session - [1e/253c0b] Submitted process > HMMSEARCH (batch_22)
Jun-02 12:11:44.541 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1e/253c0b18874b3fe67911506bf4f716]
Jun-02 12:11:44.563 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:44.563 [Task submitter] INFO  nextflow.Session - [64/9908b2] Submitted process > HMMSEARCH (batch_12)
Jun-02 12:11:44.714 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/c7f3597980002441224c1e2ce1ad44]
Jun-02 12:11:44.727 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:44.728 [Task submitter] INFO  nextflow.Session - [79/6578af] Submitted process > HMMSEARCH (batch_7)
Jun-02 12:11:50.821 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/64/9908b2f2aa3fd8880f75e259b542fa]
Jun-02 12:11:50.841 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:50.842 [Task submitter] INFO  nextflow.Session - [33/2beb2d] Submitted process > HMMSEARCH (batch_17)
Jun-02 12:11:50.867 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/6578af6cf02a8eb3236e79ddc27974]
Jun-02 12:11:50.882 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:50.883 [Task submitter] INFO  nextflow.Session - [69/a303a5] Submitted process > HMMSEARCH (batch_21)
Jun-02 12:11:57.498 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/33/2beb2dba64317e47d9abbf0627084c]
Jun-02 12:11:57.517 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:57.517 [Task submitter] INFO  nextflow.Session - [ed/56ff31] Submitted process > HMMSEARCH (batch_29)
Jun-02 12:11:57.834 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/69/a303a5cc119ef6115795f97b76384a]
Jun-02 12:11:57.854 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:11:57.854 [Task submitter] INFO  nextflow.Session - [ef/fa5baf] Submitted process > HMMSEARCH (batch_23)
Jun-02 12:12:04.913 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ed/56ff31a267a2600d2835ed4dbc1646]
Jun-02 12:12:04.944 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:04.944 [Task submitter] INFO  nextflow.Session - [33/3f7111] Submitted process > HMMSEARCH (batch_39)
Jun-02 12:12:05.266 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/fa5baf1ab494056425a02f832a609d]
Jun-02 12:12:05.281 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:05.282 [Task submitter] INFO  nextflow.Session - [df/e7728d] Submitted process > HMMSEARCH (batch_32)
Jun-02 12:12:12.741 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/33/3f7111fa474331e4ce0f463422853d]
Jun-02 12:12:12.766 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:12.767 [Task submitter] INFO  nextflow.Session - [aa/6d8ba2] Submitted process > HMMSEARCH (batch_3)
Jun-02 12:12:12.987 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/e7728dbbc1ec53de0053e29ef74b5d]
Jun-02 12:12:13.006 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:13.007 [Task submitter] INFO  nextflow.Session - [f5/50d35c] Submitted process > HMMSEARCH (batch_2)
Jun-02 12:12:20.465 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/aa/6d8ba252feb9d1c0b40f432bb82de2]
Jun-02 12:12:20.489 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:20.489 [Task submitter] INFO  nextflow.Session - [1e/3953a6] Submitted process > HMMSEARCH (batch_10)
Jun-02 12:12:20.922 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/50d35c90857513413e37de9387920a]
Jun-02 12:12:20.949 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:20.950 [Task submitter] INFO  nextflow.Session - [92/9881f8] Submitted process > HMMSEARCH (batch_13)
Jun-02 12:12:28.258 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1e/3953a6a932781455455c2fded50a5a]
Jun-02 12:12:28.274 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:28.274 [Task submitter] INFO  nextflow.Session - [2a/15be58] Submitted process > HMMSEARCH (batch_25)
Jun-02 12:12:28.664 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/92/9881f8a7c0c4475d95c6fc3be819dd]
Jun-02 12:12:28.678 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:28.679 [Task submitter] INFO  nextflow.Session - [a4/a0b9a4] Submitted process > HMMSEARCH (batch_16)
Jun-02 12:12:35.800 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/15be58e0322e5e6ed71815d5146754]
Jun-02 12:12:35.825 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:35.826 [Task submitter] INFO  nextflow.Session - [d9/6046cc] Submitted process > HMMSEARCH (batch_5)
Jun-02 12:12:36.156 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a4/a0b9a4ce74180acfcb40f0f1cdc15d]
Jun-02 12:12:36.169 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:36.170 [Task submitter] INFO  nextflow.Session - [09/59aac5] Submitted process > HMMSEARCH (batch_6)
Jun-02 12:12:43.497 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d9/6046cc2eed2c10c8caedd4695928f9]
Jun-02 12:12:43.513 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:43.513 [Task submitter] INFO  nextflow.Session - [d4/4e78b2] Submitted process > HMMSEARCH (batch_9)
Jun-02 12:12:44.017 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/09/59aac5aaaadfdbe4b33a1ab384b220]
Jun-02 12:12:44.053 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:44.054 [Task submitter] INFO  nextflow.Session - [9c/65bee1] Submitted process > HMMSEARCH (batch_28)
Jun-02 12:12:51.037 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d4/4e78b226be96c93b4f6dc852e727d3]
Jun-02 12:12:51.071 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:51.072 [Task submitter] INFO  nextflow.Session - [12/7f290f] Submitted process > HMMSEARCH (batch_31)
Jun-02 12:12:51.513 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9c/65bee1293347276d28e882d19bc944]
Jun-02 12:12:51.550 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:51.550 [Task submitter] INFO  nextflow.Session - [39/5483c5] Submitted process > HMMSEARCH (batch_27)
Jun-02 12:12:58.329 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/7f290fd12aecd317455b90445a9655]
Jun-02 12:12:58.348 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:58.349 [Task submitter] INFO  nextflow.Session - [92/7b2285] Submitted process > HMMSEARCH (batch_53)
Jun-02 12:12:59.035 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/39/5483c5999620c557f3245eb4388e60]
Jun-02 12:12:59.069 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:12:59.070 [Task submitter] INFO  nextflow.Session - [1c/5c5e0a] Submitted process > HMMSEARCH (batch_18)
Jun-02 12:13:05.913 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/92/7b22852e96e33c85ebe0aed21eed0d]
Jun-02 12:13:05.923 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:05.924 [Task submitter] INFO  nextflow.Session - [2a/3fcdc5] Submitted process > HMMSEARCH (batch_56)
Jun-02 12:13:06.744 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/5c5e0a05c4a5f750b80bed9d974f24]
Jun-02 12:13:06.762 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:06.763 [Task submitter] INFO  nextflow.Session - [1e/995945] Submitted process > HMMSEARCH (batch_57)
Jun-02 12:13:13.285 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/3fcdc5eae3b32385235e1cd5aa5261]
Jun-02 12:13:13.304 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:13.304 [Task submitter] INFO  nextflow.Session - [c2/db5cf5] Submitted process > HMMSEARCH (batch_20)
Jun-02 12:13:14.302 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1e/995945442ee93af051f49b787f6173]
Jun-02 12:13:14.316 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:14.316 [Task submitter] INFO  nextflow.Session - [9b/5dcc17] Submitted process > HMMSEARCH (batch_15)
Jun-02 12:13:21.361 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c2/db5cf51c4c8e29b900ae16967fc401]
Jun-02 12:13:21.414 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:21.415 [Task submitter] INFO  nextflow.Session - [3b/8aecb5] Submitted process > HMMSEARCH (batch_4)
Jun-02 12:13:22.347 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9b/5dcc17156cd86efe4b2b572f0609c1]
Jun-02 12:13:22.368 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:22.368 [Task submitter] INFO  nextflow.Session - [b0/edb913] Submitted process > HMMSEARCH (batch_1)
Jun-02 12:13:29.325 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3b/8aecb5b07c96132aa906215a5b52b0]
Jun-02 12:13:29.343 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:29.343 [Task submitter] INFO  nextflow.Session - [8c/5254a3] Submitted process > HMMSEARCH (batch_24)
Jun-02 12:13:30.146 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b0/edb913837d7d5b3fa0ef124735af5b]
Jun-02 12:13:30.159 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:30.159 [Task submitter] INFO  nextflow.Session - [64/112780] Submitted process > HMMSEARCH (batch_61)
Jun-02 12:13:35.769 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8c/5254a3c741fb535c1b34c36463976c]
Jun-02 12:13:35.799 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:35.799 [Task submitter] INFO  nextflow.Session - [e9/9c3810] Submitted process > HMMSEARCH (batch_30)
Jun-02 12:13:36.496 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/64/112780aacad50d4e3b70f2939fa36b]
Jun-02 12:13:36.511 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:36.511 [Task submitter] INFO  nextflow.Session - [0d/42425f] Submitted process > HMMSEARCH (batch_11)
Jun-02 12:13:42.372 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e9/9c3810d1906f6146f478a4e1ecd9e0]
Jun-02 12:13:42.383 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:42.383 [Task submitter] INFO  nextflow.Session - [8c/bdcb07] Submitted process > HMMSEARCH (batch_47)
Jun-02 12:13:43.206 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0d/42425f399238099876e91f5e64b47f]
Jun-02 12:13:43.227 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:43.228 [Task submitter] INFO  nextflow.Session - [f9/5e66de] Submitted process > HMMSEARCH (batch_26)
Jun-02 12:13:49.529 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8c/bdcb073c1bf926bfaaee639c48be57]
Jun-02 12:13:49.543 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:49.543 [Task submitter] INFO  nextflow.Session - [58/dff1be] Submitted process > HMMSEARCH (batch_45)
Jun-02 12:13:50.330 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f9/5e66de5469c3d336155fc6f5f6b48c]
Jun-02 12:13:50.353 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:50.353 [Task submitter] INFO  nextflow.Session - [76/99fde1] Submitted process > HMMSEARCH (batch_41)
Jun-02 12:13:56.241 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/58/dff1beab19c62be70c5fbf8b4a8c08]
Jun-02 12:13:56.263 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:56.263 [Task submitter] INFO  nextflow.Session - [8a/a5f1cf] Submitted process > HMMSEARCH (batch_51)
Jun-02 12:13:56.984 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/76/99fde195eb5196ea3b3aaf4f3ea904]
Jun-02 12:13:57.005 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:13:57.005 [Task submitter] INFO  nextflow.Session - [52/b14f57] Submitted process > HMMSEARCH (batch_58)
Jun-02 12:14:03.313 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8a/a5f1cf3226c1aa51ad857c5c1b8991]
Jun-02 12:14:03.334 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:03.335 [Task submitter] INFO  nextflow.Session - [14/6b6a13] Submitted process > HMMSEARCH (batch_48)
Jun-02 12:14:04.188 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/b14f571fc7753f258f7f7dff8cf055]
Jun-02 12:14:04.224 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:04.224 [Task submitter] INFO  nextflow.Session - [67/8ae0f0] Submitted process > HMMSEARCH (batch_60)
Jun-02 12:14:10.027 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/14/6b6a138b12f2ef89b1b7854532481c]
Jun-02 12:14:10.041 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:10.041 [Task submitter] INFO  nextflow.Session - [c4/ee1610] Submitted process > HMMSEARCH (batch_37)
Jun-02 12:14:11.305 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/67/8ae0f0ac241fd853c12e27c817a39a]
Jun-02 12:14:11.318 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:11.318 [Task submitter] INFO  nextflow.Session - [3d/ea9b46] Submitted process > HMMSEARCH (batch_36)
Jun-02 12:14:16.237 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/ee1610f11586be8da455edf6ea8f92]
Jun-02 12:14:16.253 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:16.253 [Task submitter] INFO  nextflow.Session - [0e/dc3260] Submitted process > HMMSEARCH (batch_42)
Jun-02 12:14:17.523 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3d/ea9b46690dfdbe6c558358b7a01e5b]
Jun-02 12:14:17.534 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:17.534 [Task submitter] INFO  nextflow.Session - [da/70682e] Submitted process > HMMSEARCH (batch_46)
Jun-02 12:14:23.018 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0e/dc32609ecd511c8fb41bff269e404e]
Jun-02 12:14:23.035 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:23.035 [Task submitter] INFO  nextflow.Session - [73/6b26da] Submitted process > HMMSEARCH (batch_52)
Jun-02 12:14:24.462 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/da/70682e9bc06a80886ce8fb5a98772a]
Jun-02 12:14:24.477 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:24.477 [Task submitter] INFO  nextflow.Session - [a9/6d2f83] Submitted process > HMMSEARCH (batch_34)
Jun-02 12:14:30.098 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/73/6b26daf97a16ae7f1d5a8a396d1e07]
Jun-02 12:14:30.116 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:30.117 [Task submitter] INFO  nextflow.Session - [a7/7b601a] Submitted process > HMMSEARCH (batch_49)
Jun-02 12:14:31.137 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/6d2f8380a9a6500c5948ce9b031051]
Jun-02 12:14:31.154 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:31.155 [Task submitter] INFO  nextflow.Session - [6a/df0790] Submitted process > HMMSEARCH (batch_55)
Jun-02 12:14:36.249 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a7/7b601a7b834ea80d7ef50374e54959]
Jun-02 12:14:36.265 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:36.265 [Task submitter] INFO  nextflow.Session - [23/a60c2a] Submitted process > HMMSEARCH (batch_44)
Jun-02 12:14:37.317 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/df0790b0de8ff317befdcf5a1a2b56]
Jun-02 12:14:37.328 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:37.328 [Task submitter] INFO  nextflow.Session - [ce/4dc921] Submitted process > HMMSEARCH (batch_38)
Jun-02 12:14:42.494 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/23/a60c2ac57ae2ecebf13f65d8e8733d]
Jun-02 12:14:42.507 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:42.507 [Task submitter] INFO  nextflow.Session - [b1/9c2757] Submitted process > HMMSEARCH (batch_33)
Jun-02 12:14:43.490 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ce/4dc921bce8ce2e69c970add419f3e8]
Jun-02 12:14:43.501 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:43.501 [Task submitter] INFO  nextflow.Session - [f3/dae392] Submitted process > HMMSEARCH (batch_62)
Jun-02 12:14:48.458 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/9c27572c1af510cebd91febbb9f109]
Jun-02 12:14:48.479 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:48.480 [Task submitter] INFO  nextflow.Session - [68/d01f8a] Submitted process > HMMSEARCH (batch_43)
Jun-02 12:14:49.909 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f3/dae3923c5a64648786e24332059de0]
Jun-02 12:14:49.919 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:49.920 [Task submitter] INFO  nextflow.Session - [b5/d91065] Submitted process > HMMSEARCH (batch_50)
Jun-02 12:14:54.922 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/d01f8a1d72220cf35b06e3ab4407f0]
Jun-02 12:14:54.938 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:54.938 [Task submitter] INFO  nextflow.Session - [b9/db6228] Submitted process > HMMSEARCH (batch_35)
Jun-02 12:14:56.375 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b5/d9106549bd97fccd3787a61b3f222e]
Jun-02 12:14:56.388 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:14:56.388 [Task submitter] INFO  nextflow.Session - [26/f4c0cb] Submitted process > HMMSEARCH (batch_40)
Jun-02 12:15:01.112 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/db622843bdbc330c9fbfe9794f0d52]
Jun-02 12:15:01.121 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:01.122 [Task submitter] INFO  nextflow.Session - [f8/b8d8bf] Submitted process > HMMSEARCH (batch_54)
Jun-02 12:15:02.923 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/26/f4c0cbffc1cc449f8f47cc4050be79]
Jun-02 12:15:02.953 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:02.953 [Task submitter] INFO  nextflow.Session - [91/054313] Submitted process > HMMSEARCH (batch_59)
Jun-02 12:15:07.617 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/b8d8bf999752cafb52ad9dc1b5c359]
Jun-02 12:15:07.645 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:07.646 [Task submitter] INFO  nextflow.Session - [ff/29c119] Submitted process > HMMSEARCH (batch_66)
Jun-02 12:15:08.616 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ff/29c119662cafb1d444af70a9c9c592]
Jun-02 12:15:08.632 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:08.632 [Task submitter] INFO  nextflow.Session - [08/034d84] Submitted process > HMMSEARCH (batch_64)
Jun-02 12:15:09.394 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/91/054313b628484912baca31b03a2807]
Jun-02 12:15:09.411 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:09.411 [Task submitter] INFO  nextflow.Session - [d5/2e3af0] Submitted process > HMMSEARCH (batch_63)
Jun-02 12:15:15.083 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/034d84ab3c93f67dbabe52fdcb1f7e]
Jun-02 12:15:15.093 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:15.094 [Task submitter] INFO  nextflow.Session - [11/d7abf8] Submitted process > HMMSEARCH (batch_65)
Jun-02 12:15:16.306 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d5/2e3af02201eb026f2d6db3977ab411]
Jun-02 12:15:16.319 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.319 [Task submitter] INFO  nextflow.Session - [28/2d2b19] Submitted process > PROCESS_HITS (batch_8)
Jun-02 12:15:16.332 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.333 [Task submitter] INFO  nextflow.Session - [04/30c812] Submitted process > PROCESS_HITS (batch_14)
Jun-02 12:15:16.352 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.352 [Task submitter] INFO  nextflow.Session - [85/d14f9c] Submitted process > PROCESS_HITS (batch_22)
Jun-02 12:15:16.365 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.365 [Task submitter] INFO  nextflow.Session - [a1/9fc3e7] Submitted process > PROCESS_HITS (batch_19)
Jun-02 12:15:16.382 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.383 [Task submitter] INFO  nextflow.Session - [24/78acdc] Submitted process > PROCESS_HITS (batch_12)
Jun-02 12:15:16.394 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.395 [Task submitter] INFO  nextflow.Session - [16/f82a84] Submitted process > PROCESS_HITS (batch_7)
Jun-02 12:15:16.409 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.410 [Task submitter] INFO  nextflow.Session - [55/91fb5c] Submitted process > PROCESS_HITS (batch_17)
Jun-02 12:15:16.425 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:16.426 [Task submitter] INFO  nextflow.Session - [b9/313777] Submitted process > PROCESS_HITS (batch_21)
Jun-02 12:15:21.417 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/11/d7abf8d16e8df1e935b38380954641]
Jun-02 12:15:21.468 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.468 [Task submitter] INFO  nextflow.Session - [29/ee0839] Submitted process > PROCESS_HITS (batch_29)
Jun-02 12:15:21.491 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.491 [Task submitter] INFO  nextflow.Session - [e5/e2690f] Submitted process > PROCESS_HITS (batch_23)
Jun-02 12:15:21.510 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.511 [Task submitter] INFO  nextflow.Session - [5e/95252f] Submitted process > PROCESS_HITS (batch_39)
Jun-02 12:15:21.520 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.520 [Task submitter] INFO  nextflow.Session - [1c/2c5399] Submitted process > PROCESS_HITS (batch_32)
Jun-02 12:15:21.529 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.530 [Task submitter] INFO  nextflow.Session - [d7/1698bc] Submitted process > PROCESS_HITS (batch_3)
Jun-02 12:15:21.541 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.541 [Task submitter] INFO  nextflow.Session - [14/8c7f18] Submitted process > PROCESS_HITS (batch_2)
Jun-02 12:15:21.555 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.555 [Task submitter] INFO  nextflow.Session - [ee/242922] Submitted process > PROCESS_HITS (batch_10)
Jun-02 12:15:21.567 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:21.568 [Task submitter] INFO  nextflow.Session - [b0/a3c8e4] Submitted process > PROCESS_HITS (batch_13)
Jun-02 12:15:29.965 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/85/d14f9c2f75506a31e6e13cb597d6b6]
Jun-02 12:15:29.986 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:29.986 [Task submitter] INFO  nextflow.Session - [31/f889f8] Submitted process > PROCESS_HITS (batch_25)
Jun-02 12:15:30.181 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/28/2d2b19d38302ad1d689d3951efcd80]
Jun-02 12:15:30.201 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:30.201 [Task submitter] INFO  nextflow.Session - [87/16fdde] Submitted process > PROCESS_HITS (batch_16)
Jun-02 12:15:30.206 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/04/30c812834ed0570165dfd2239043ec]
Jun-02 12:15:30.229 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:30.229 [Task submitter] INFO  nextflow.Session - [d8/b8d213] Submitted process > PROCESS_HITS (batch_5)
Jun-02 12:15:30.357 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/3137779424babdd7a8ca735d1174e7]
Jun-02 12:15:30.382 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:30.382 [Task submitter] INFO  nextflow.Session - [2a/67b9fa] Submitted process > PROCESS_HITS (batch_6)
Jun-02 12:15:30.534 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/9fc3e7d30c3e31deb208cb154e68a8]
Jun-02 12:15:30.555 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:30.555 [Task submitter] INFO  nextflow.Session - [3e/de82fa] Submitted process > PROCESS_HITS (batch_9)
Jun-02 12:15:30.660 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/55/91fb5c5f042053a750ea021ac3de4d]
Jun-02 12:15:30.686 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/78acdc3297ca9021b3390bd1e78745]
Jun-02 12:15:30.697 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:30.698 [Task submitter] INFO  nextflow.Session - [f7/4e5284] Submitted process > PROCESS_HITS (batch_28)
Jun-02 12:15:30.708 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:30.708 [Task submitter] INFO  nextflow.Session - [99/b73a82] Submitted process > PROCESS_HITS (batch_31)
Jun-02 12:15:31.070 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/f82a846dc1a5fa8e7245baa4d5e628]
Jun-02 12:15:31.091 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:31.091 [Task submitter] INFO  nextflow.Session - [66/c911c2] Submitted process > PROCESS_HITS (batch_27)
Jun-02 12:15:34.528 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d7/1698bc35ec1b9d7b2cd611cabb096a]
Jun-02 12:15:34.548 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:34.548 [Task submitter] INFO  nextflow.Session - [24/f4530e] Submitted process > PROCESS_HITS (batch_53)
Jun-02 12:15:34.632 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/14/8c7f186e679e93c5b88f6f4b7031eb]
Jun-02 12:15:34.648 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:34.649 [Task submitter] INFO  nextflow.Session - [d4/e5117b] Submitted process > PROCESS_HITS (batch_18)
Jun-02 12:15:34.681 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/ee08398ddd1db775551c45273f3922]
Jun-02 12:15:34.696 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:34.696 [Task submitter] INFO  nextflow.Session - [01/1cd62f] Submitted process > PROCESS_HITS (batch_56)
Jun-02 12:15:34.905 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b0/a3c8e4ef25092a518e5b8c52d0e343]
Jun-02 12:15:34.934 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:34.934 [Task submitter] INFO  nextflow.Session - [6e/0ad6bf] Submitted process > PROCESS_HITS (batch_57)
Jun-02 12:15:34.950 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/2c5399c5e36201be6ebe8f711868ad]
Jun-02 12:15:34.962 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ee/242922165398476d5588c0abff8281]
Jun-02 12:15:34.990 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:34.990 [Task submitter] INFO  nextflow.Session - [93/c5b50c] Submitted process > PROCESS_HITS (batch_20)
Jun-02 12:15:35.004 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:35.005 [Task submitter] INFO  nextflow.Session - [da/8e9113] Submitted process > PROCESS_HITS (batch_15)
Jun-02 12:15:35.258 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/95252f583da1308a6020a906132427]
Jun-02 12:15:35.276 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:35.277 [Task submitter] INFO  nextflow.Session - [42/071c5b] Submitted process > PROCESS_HITS (batch_4)
Jun-02 12:15:35.313 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e2690f28b8550d86a46014af274db4]
Jun-02 12:15:35.332 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:35.332 [Task submitter] INFO  nextflow.Session - [7b/4e7319] Submitted process > PROCESS_HITS (batch_1)
Jun-02 12:15:42.555 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/31/f889f87f69d19c9fecc3e0ab969a02]
Jun-02 12:15:42.574 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:42.575 [Task submitter] INFO  nextflow.Session - [95/24f22b] Submitted process > PROCESS_HITS (batch_24)
Jun-02 12:15:42.686 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/87/16fddefdfcc46e4bfbaa5991419d41]
Jun-02 12:15:42.710 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:42.710 [Task submitter] INFO  nextflow.Session - [88/16c650] Submitted process > PROCESS_HITS (batch_61)
Jun-02 12:15:42.877 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d8/b8d21363e89a7db59e1c43049820de]
Jun-02 12:15:42.898 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:42.898 [Task submitter] INFO  nextflow.Session - [97/c59e31] Submitted process > PROCESS_HITS (batch_30)
Jun-02 12:15:43.085 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3e/de82faa6ecd77cfb6644cd466d9b29]
Jun-02 12:15:43.108 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:43.108 [Task submitter] INFO  nextflow.Session - [84/7e6030] Submitted process > PROCESS_HITS (batch_11)
Jun-02 12:15:43.373 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f7/4e5284f957929dad7d965dedfdc628]
Jun-02 12:15:43.402 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:43.402 [Task submitter] INFO  nextflow.Session - [4b/65a377] Submitted process > PROCESS_HITS (batch_47)
Jun-02 12:15:43.406 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/67b9fa2517b0b29ab11ab6cc95f1b5]
Jun-02 12:15:43.427 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:43.427 [Task submitter] INFO  nextflow.Session - [a8/3bab45] Submitted process > PROCESS_HITS (batch_26)
Jun-02 12:15:43.508 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/66/c911c2cb838ec2369ea6645ed87368]
Jun-02 12:15:43.532 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:43.532 [Task submitter] INFO  nextflow.Session - [7b/8ba79f] Submitted process > PROCESS_HITS (batch_45)
Jun-02 12:15:43.916 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/99/b73a824b24ff7cf4d882e917a45e92]
Jun-02 12:15:43.934 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:43.934 [Task submitter] INFO  nextflow.Session - [a5/be7ff2] Submitted process > PROCESS_HITS (batch_41)
Jun-02 12:15:47.142 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d4/e5117b2b93a726ffcad4edaf6a3d79]
Jun-02 12:15:47.173 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:47.174 [Task submitter] INFO  nextflow.Session - [51/a9e19f] Submitted process > PROCESS_HITS (batch_51)
Jun-02 12:15:47.187 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/01/1cd62f8238d6f22903c0ee779ae8aa]
Jun-02 12:15:47.210 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:47.210 [Task submitter] INFO  nextflow.Session - [f1/5ebc32] Submitted process > PROCESS_HITS (batch_58)
Jun-02 12:15:47.738 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/f4530e14643a304e926f7806e7b8b2]
Jun-02 12:15:47.741 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/93/c5b50c0f4c3fcbcb50e965ed1850ce]
Jun-02 12:15:47.757 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:47.758 [Task submitter] INFO  nextflow.Session - [08/46eae8] Submitted process > PROCESS_HITS (batch_48)
Jun-02 12:15:47.776 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:47.776 [Task submitter] INFO  nextflow.Session - [28/1b8421] Submitted process > PROCESS_HITS (batch_60)
Jun-02 12:15:47.905 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/071c5b3049a73981a86903c88978ac]
Jun-02 12:15:47.931 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:47.931 [Task submitter] INFO  nextflow.Session - [87/fa7fd7] Submitted process > PROCESS_HITS (batch_37)
Jun-02 12:15:47.946 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/da/8e91133266e991b694a3c3593cfb1e]
Jun-02 12:15:47.986 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:47.986 [Task submitter] INFO  nextflow.Session - [48/5bd609] Submitted process > PROCESS_HITS (batch_36)
Jun-02 12:15:48.333 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7b/4e73190051edd078aa812fdd214a2a]
Jun-02 12:15:48.359 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:48.360 [Task submitter] INFO  nextflow.Session - [c1/aeaf50] Submitted process > PROCESS_HITS (batch_42)
Jun-02 12:15:48.370 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6e/0ad6bfc35c777d6f2c6f064b458f5e]
Jun-02 12:15:48.400 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:48.400 [Task submitter] INFO  nextflow.Session - [4f/ef35dc] Submitted process > PROCESS_HITS (batch_46)
Jun-02 12:15:57.390 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/88/16c6503ff9aa306b1bb78d89b367d5]
Jun-02 12:15:57.420 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:57.421 [Task submitter] INFO  nextflow.Session - [32/3a16fd] Submitted process > PROCESS_HITS (batch_52)
Jun-02 12:15:58.031 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/c59e314122f292db8ff632f46509f9]
Jun-02 12:15:58.053 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/95/24f22b5075a7d463ffcb8301d68caf]
Jun-02 12:15:58.057 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:58.058 [Task submitter] INFO  nextflow.Session - [f5/a6b8af] Submitted process > PROCESS_HITS (batch_34)
Jun-02 12:15:58.065 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/7e6030134438260af6a0f3f116eaf2]
Jun-02 12:15:58.087 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:58.087 [Task submitter] INFO  nextflow.Session - [10/733789] Submitted process > PROCESS_HITS (batch_49)
Jun-02 12:15:58.108 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:58.109 [Task submitter] INFO  nextflow.Session - [2a/51e49d] Submitted process > PROCESS_HITS (batch_55)
Jun-02 12:15:58.472 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7b/8ba79f5197e3ec948f58dab1518be9]
Jun-02 12:15:58.496 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:58.497 [Task submitter] INFO  nextflow.Session - [7c/082543] Submitted process > PROCESS_HITS (batch_44)
Jun-02 12:15:58.631 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a8/3bab45e221aab61365d449230080ba]
Jun-02 12:15:58.648 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:58.648 [Task submitter] INFO  nextflow.Session - [d3/37d37a] Submitted process > PROCESS_HITS (batch_38)
Jun-02 12:15:58.753 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/be7ff24994c4e83f4daec491770164]
Jun-02 12:15:58.772 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:58.772 [Task submitter] INFO  nextflow.Session - [bf/44a9df] Submitted process > PROCESS_HITS (batch_33)
Jun-02 12:15:58.780 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4b/65a3770720b87499472cd1ee2b7a54]
Jun-02 12:15:58.801 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:15:58.801 [Task submitter] INFO  nextflow.Session - [40/d7e40c] Submitted process > PROCESS_HITS (batch_62)
Jun-02 12:16:03.022 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/5ebc32b664a1da8c98c45f7f920630]
Jun-02 12:16:03.049 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:03.049 [Task submitter] INFO  nextflow.Session - [12/6b4c98] Submitted process > PROCESS_HITS (batch_43)
Jun-02 12:16:03.080 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/51/a9e19f84e2d39cd4e6ed47f1818936]
Jun-02 12:16:03.099 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:03.100 [Task submitter] INFO  nextflow.Session - [b2/ef117c] Submitted process > PROCESS_HITS (batch_50)
Jun-02 12:16:03.414 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/48/5bd6091d56125180a40a2beb6616b0]
Jun-02 12:16:03.443 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:03.444 [Task submitter] INFO  nextflow.Session - [ae/3ee3ad] Submitted process > PROCESS_HITS (batch_35)
Jun-02 12:16:03.627 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/46eae8f9237200018340ab24b9589d]
Jun-02 12:16:03.647 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:03.647 [Task submitter] INFO  nextflow.Session - [4b/1888d9] Submitted process > PROCESS_HITS (batch_40)
Jun-02 12:16:03.663 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/28/1b8421543523dac83bb37d5f754631]
Jun-02 12:16:03.696 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:03.696 [Task submitter] INFO  nextflow.Session - [53/bb85cb] Submitted process > PROCESS_HITS (batch_54)
Jun-02 12:16:03.898 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/87/fa7fd78c44f07abdb00ea9ed131a8f]
Jun-02 12:16:03.942 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:03.943 [Task submitter] INFO  nextflow.Session - [b6/cfe284] Submitted process > PROCESS_HITS (batch_66)
Jun-02 12:16:04.276 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4f/ef35dcd677b54e3c33064ffe09e1cd]
Jun-02 12:16:04.291 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:04.291 [Task submitter] INFO  nextflow.Session - [17/749321] Submitted process > PROCESS_HITS (batch_59)
Jun-02 12:16:04.533 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c1/aeaf509560ca9529acf6b4ffb3926a]
Jun-02 12:16:04.567 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:04.567 [Task submitter] INFO  nextflow.Session - [3f/167ce4] Submitted process > PROCESS_HITS (batch_64)
Jun-02 12:16:06.183 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/cfe2841ff1ae4273d72dee93a1f73f]
Jun-02 12:16:06.206 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:06.207 [Task submitter] INFO  nextflow.Session - [34/9d69ba] Submitted process > PROCESS_HITS (batch_63)
Jun-02 12:16:13.154 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/32/3a16fd72d12aa813c21f5dc673c5ea]
Jun-02 12:16:13.170 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:16:13.170 [Task submitter] INFO  nextflow.Session - [9a/235130] Submitted process > PROCESS_HITS (batch_65)
Jun-02 12:16:13.565 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/a6b8af92fff0b1040c95b120b11f6a]
Jun-02 12:16:13.624 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/51e49d7ac0f7101cb7f75d21942401]
Jun-02 12:16:13.877 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/10/7337893aa2e44ec4f78fedb1f44d92]
Jun-02 12:16:14.034 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/44a9df9f84a39df4401840bea71128]
Jun-02 12:16:14.228 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7c/0825439ff3e03e76be0bd903dab20c]
Jun-02 12:16:14.228 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d3/37d37ae6bfe81ba978cc1c0fc7648e]
Jun-02 12:16:14.823 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/d7e40c8abecb39935099c7992b4592]
Jun-02 12:16:17.500 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/6b4c987a524900bcd2c18d73e7175f]
Jun-02 12:16:17.774 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ae/3ee3ad75ade249c771bb41dfc079d3]
Jun-02 12:16:17.841 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/ef117c8b28470816e4cdd2d4ce05a2]
Jun-02 12:16:18.223 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4b/1888d91721d589dad33d27e1e6fcf1]
Jun-02 12:16:18.304 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/53/bb85cb8480de99e50b0e90bfb04f7d]
Jun-02 12:16:18.767 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/74932141274bbf980214a9c6cf99ba]
Jun-02 12:16:19.066 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3f/167ce4a3d5aa0f672fb0ece6d185dd]
Jun-02 12:16:20.034 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/34/9d69ba1dd288f1936012c1f54f2135]
Jun-02 12:16:24.041 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 133; name: PROCESS_HITS (batch_65); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9a/23513013557357e00ad0b8ad563d54]
Jun-02 12:16:25.887 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9a/23513013557357e00ad0b8ad563d54]
Jun-02 12:17:54.801 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:17:54.801 [Task submitter] INFO  nextflow.Session - [b2/44df7d] Submitted process > CREATE_DATAFRAME (1)
Jun-02 12:21:24.051 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/44df7d15d38b57b59512869bf36647]
Jun-02 12:26:24.090 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/44df7d15d38b57b59512869bf36647]
Jun-02 12:31:24.151 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/44df7d15d38b57b59512869bf36647]
Jun-02 12:36:24.173 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/44df7d15d38b57b59512869bf36647]
Jun-02 12:41:24.208 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/44df7d15d38b57b59512869bf36647]
Jun-02 12:46:24.246 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/44df7d15d38b57b59512869bf36647]
Jun-02 12:47:28.588 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/44df7d15d38b57b59512869bf36647]
Jun-02 12:47:28.615 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:47:28.615 [Task submitter] INFO  nextflow.Session - [ca/a5392c] Submitted process > AUTOENCODER (1)
Jun-02 12:48:33.082 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: AUTOENCODER (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ca/a5392cb4c820cf1a34e05495fbf13f]
Jun-02 12:48:33.144 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 12:48:33.145 [Task submitter] INFO  nextflow.Session - [41/235f4e] Submitted process > MODEL_PREDICT (1)
Jun-02 12:48:34.998 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 136; name: MODEL_PREDICT (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/41/235f4e8a59d07b6d3b2972458070d7]
Jun-02 12:48:35.001 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-02 12:48:35.099 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-02 12:48:35.099 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-02 12:48:35.107 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jun-02 12:48:35.108 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jun-02 12:48:35.112 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=136; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=4h 29m 46s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=19; peakCpus=38; peakMemory=76 GB; ]
Jun-02 12:48:35.247 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-02 12:48:35.265 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-02 12:48:35.265 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
