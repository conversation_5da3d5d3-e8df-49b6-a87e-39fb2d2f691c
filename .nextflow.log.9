May-28 11:46:39.883 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
May-28 11:46:39.970 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-28 11:46:39.990 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-28 11:46:40.013 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-28 11:46:40.014 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-28 11:46:40.017 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-28 11:46:40.029 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-28 11:46:40.047 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 11:46:40.049 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 11:46:40.087 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-28 11:46:40.090 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7e97551f] - activable => nextflow.secret.LocalSecretsProvider@7e97551f
May-28 11:46:40.102 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-28 11:46:40.583 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-28 11:46:40.599 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [jolly_shockley] DSL2 - revision: 572cf1876f
May-28 11:46:40.600 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-28 11:46:40.601 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-28 11:46:40.646 [main] DEBUG nextflow.Session - Session UUID: 6d670c6f-6991-465f-a17e-6083e8266b29
May-28 11:46:40.647 [main] DEBUG nextflow.Session - Run name: jolly_shockley
May-28 11:46:40.649 [main] DEBUG nextflow.Session - Executor pool size: 32
May-28 11:46:40.658 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-28 11:46:40.663 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 11:46:40.684 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (7.8 GB) - Swap: 8 GB (48 KB)
May-28 11:46:40.719 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-28 11:46:40.753 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-28 11:46:40.768 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-28 11:46:40.773 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-28 11:46:40.798 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-28 11:46:40.807 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-28 11:46:40.942 [main] DEBUG nextflow.Session - Session start
May-28 11:46:41.607 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-28 11:46:41.643 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-28 11:46:41.749 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-28 11:46:41.759 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:46:41.759 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:46:41.769 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-28 11:46:41.774 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-28 11:46:41.776 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-28 11:46:41.797 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-28 11:46:41.860 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-28 11:46:41.878 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:46:41.878 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:46:41.880 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-28 11:46:41.882 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-28 11:46:41.896 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-28 11:46:41.898 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:46:41.898 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:46:41.899 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-28 11:46:41.900 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-28 11:46:41.909 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-28 11:46:41.911 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:46:41.911 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:46:41.912 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-28 11:46:41.920 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:46:41.920 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:46:41.921 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-28 11:46:41.928 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:46:41.928 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:46:41.930 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-28 11:46:41.933 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-28 11:46:41.936 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
May-28 11:46:41.948 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-28 11:46:41.949 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 11:46:41.950 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 11:46:41.952 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 11:46:41.953 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 11:46:41.955 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-28 11:46:41.955 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-28 11:46:41.957 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-28 11:46:41.958 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_f5dec23c1b3efd54: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-28 11:46:41.959 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-28 11:46:41.962 [main] DEBUG nextflow.Session - Session await
May-28 11:46:42.094 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:46:42.096 [Task submitter] INFO  nextflow.Session - [7a/abebcf] Submitted process > PRODIGAL (1)
May-28 11:46:42.116 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:46:42.116 [Task submitter] INFO  nextflow.Session - [1c/135944] Submitted process > CREATE_DATAFRAME (1)
May-28 11:46:42.672 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 1; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/******************************]
May-28 11:46:42.673 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 11:46:42.682 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME (1); work-dir=/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/******************************
  error [nextflow.exception.ProcessFailedException]: Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)
May-28 11:46:42.700 [TaskFinalizer-1] ERROR nextflow.processor.TaskProcessor - Error executing process > 'CREATE_DATAFRAME (1)'

Caused by:
  Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)


Command executed:

  # Create directory structure expected by the script
  mkdir -p csv_hits
  for i in $(seq 1 66); do
      mkdir -p csv_hits/batch_$i
  done
  
  # Debug: List all CSV files
  echo "All CSV files to process:"
  ls -la main.nf || echo "No CSV files found"
  
  # Get the project root directory (where main.nf is located)
  PROJECT_ROOT=$(dirname $(readlink -f main.nf))
  echo "Project root: $PROJECT_ROOT"
  
  # Get the output directory path
  OUTDIR="$PROJECT_ROOT/results"
  echo "Output directory: $OUTDIR"
  
  # Copy files from the output directory's processed_hits directly
  echo "Copying files from $OUTDIR/processed_hits"
  
  # Check if the directory exists
  if [ ! -d "$OUTDIR/processed_hits" ]; then
      echo "Directory $OUTDIR/processed_hits does not exist"
      echo "Current directory: $(pwd)"
      echo "Listing current directory:"
      ls -la
      echo "Listing project root:"
      ls -la $PROJECT_ROOT
      exit 1
  fi
  
  # Find all batch directories in the processed_hits directory
  BATCH_DIRS=$(find $OUTDIR/processed_hits -maxdepth 1 -type d -name "batch_*" | sort)
  
  if [ -z "$BATCH_DIRS" ]; then
      echo "No batch directories found in $OUTDIR/processed_hits"
      exit 1
  fi
  
  # Process each batch directory
  for BATCH_DIR in $BATCH_DIRS; do
      BATCH_NAME=$(basename $BATCH_DIR)
      echo "Processing $BATCH_NAME"
  
      # Extract batch number
      BATCH_NUM=$(echo $BATCH_NAME | sed 's/batch_//')
  
      # Create the corresponding directory in csv_hits if it doesn't exist
      mkdir -p csv_hits/$BATCH_NAME
  
      # Copy CSV files using absolute paths
      find "$OUTDIR/processed_hits/$BATCH_NAME" -name "*.csv" -exec cp {} csv_hits/$BATCH_NAME/ \;
  done
  
  # Debug: List the contents of the csv_hits directory
  echo "Contents of csv_hits directory:"
  find csv_hits -type f | sort
  
  # Run the script with the organized directory structure
  if [ "true" = "true" ]; then
      echo "Running without taxonomic classification (--skip_tax enabled)"
      python create_unique_df_hits_optimized.py csv_hits --skip_tax final_results.csv
  else
      echo "Running with taxonomic classification"
      python create_unique_df_hits_optimized.py csv_hits nextflow.config final_results.csv
  fi

Command exit status:
  1

Command output:
  -rw-rw-r-- 1 <USER> <GROUP> 4579 May 28 11:46 .command.run
  -rw-rw-r-- 1 <USER> <GROUP> 2139 May 28 11:46 .command.sh
  lrwxrwxrwx 1 laureli nogroup  114 May 28 11:46 create_unique_df_hits_optimized.py -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/bin/create_unique_df_hits_optimized.py
  drwxrwsr-x 2 laureli nogroup 4096 May 28 11:46 csv_hits
  lrwxrwxrwx 1 laureli nogroup   83 May 28 11:46 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
  lrwxrwxrwx 1 laureli nogroup   91 May 28 11:46 nextflow.config -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
  Listing project root:
  total 9484627
  drwxrwsr-x 2 laureli nogroup       4096 May 28 11:46 .
  drwxrwsr-x 2 laureli nogroup       4096 May 19 05:29 ..
  drwxrwsr-x 2 laureli nogroup       4096 May 28 11:46 .nextflow
  -rw-rw-r-- 1 <USER> <GROUP>      10074 May 28 11:46 .nextflow.log
  -rw-rw-r-- 1 <USER> <GROUP>      22994 May 28 10:26 .nextflow.log.1
  -rw-rw-r-- 1 <USER> <GROUP>      95895 May 27 16:00 .nextflow.log.2
  -rw-rw-r-- 1 <USER> <GROUP>    4190374 May 19 05:48 GCF_008369605.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>    4809616 Oct 26  2022 GCF_025823245.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>       4872 May 19 22:26 README.md
  -rw-rw-r-- 1 <USER> <GROUP>      12855 May 20 12:42 autoencoder_tool.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 09:22 backup
  drwxrwsr-x 2 laureli nogroup       4096 May 22 04:29 backup_parallel
  -rw-rw-r-- 1 <USER> <GROUP>     487562 May 20 13:56 best_refined_model_RS.pkl
  drwxrwsr-x 2 laureli nogroup       4096 May 19 07:56 bin
  -rwxrwxr-x 1 <USER> <GROUP>        712 May 19 07:56 build_containers.sh
  -rw-rw-r-- 1 <USER> <GROUP>       5569 May 20 12:19 check_compatibility.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 08:55 containers
  -rw-rw-r-- 1 <USER> <GROUP>       8199 May 19 05:37 convert_hits.py
  -rw-rw-r-- 1 <USER> <GROUP>       9583 May 19 05:31 create_unique_df_hits_optimized.py
  -rw-rw-r-- 1 <USER> <GROUP>       5846 May 20 12:19 encode_final_results.py
  -rwxrwxr-x 1 <USER> <GROUP>       1612 May 21 02:28 fix_workflow.sh
  -rwxrwxr-x 1 <USER> <GROUP>       4552 May 19 05:30 hmmsearch_python.py
  -rw-rw-r-- 1 <USER> <GROUP>      12303 May 27 11:26 main.nf
  -rw-rw-r-- 1 <USER> <GROUP>      10864 May 21 09:56 main.nf.fixed
  drwxrwsr-x 2 laureli nogroup       4096 May 27 08:23 ml
  -rw-rw-r-- 1 <USER> <GROUP>       9532 May 20 13:56 model_load_and_predict.py
  -rw-rw-r-- 1 <USER> <GROUP>       1922 May 27 10:42 nextflow.config
  drwxrwsr-x 2 laureli nogroup       4096 May 22 05:06 ppred
  -rwxrwxr-x 1 <USER> <GROUP>       1741 May 20 10:39 process_existing_results.sh
  -rw-rw-r-- 1 <USER> <GROUP>       2739 May 20 10:31 process_results.py
  -rw-rw-r-- 1 <USER> <GROUP>       1231 May 19 07:11 project_summary.txt
  -rw-rw-r-- 1 <USER> <GROUP>         22 May 28 01:52 remove.log
  -rw-rw-r-- 1 <USER> <GROUP>    2944023 May 27 09:15 report-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 results
  -rw-rw-r-- 1 <USER> <GROUP> 9698149560 May 20 12:44 robustscaler_enc1024_layers1.h5
  -rw-rw-r-- 1 <USER> <GROUP>    1171829 May 20 12:43 robustscaler_enc1024_layers1.pkl
  -rw-rw-r-- 1 <USER> <GROUP>       2720 May 20 10:34 run_final_step.py
  -rwxrwxr-x 1 <USER> <GROUP>      11673 May 19 05:35 run_gtdbtk.py
  -rwxrwxr-x 1 <USER> <GROUP>       6093 May 19 05:29 run_prodigal_and_rename.py
  -rw-rw-r-- 1 <USER> <GROUP>        261 May 19 23:33 test_path.nf
  -rw-rw-r-- 1 <USER> <GROUP>     252307 May 27 09:15 timeline-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 11:46 work

Command error:
  -rw-rw-r-- 1 <USER> <GROUP> 4579 May 28 11:46 .command.run
  -rw-rw-r-- 1 <USER> <GROUP> 2139 May 28 11:46 .command.sh
  lrwxrwxrwx 1 laureli nogroup  114 May 28 11:46 create_unique_df_hits_optimized.py -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/bin/create_unique_df_hits_optimized.py
  drwxrwsr-x 2 laureli nogroup 4096 May 28 11:46 csv_hits
  lrwxrwxrwx 1 laureli nogroup   83 May 28 11:46 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
  lrwxrwxrwx 1 laureli nogroup   91 May 28 11:46 nextflow.config -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
  Listing project root:
  total 9484627
  drwxrwsr-x 2 laureli nogroup       4096 May 28 11:46 .
  drwxrwsr-x 2 laureli nogroup       4096 May 19 05:29 ..
  drwxrwsr-x 2 laureli nogroup       4096 May 28 11:46 .nextflow
  -rw-rw-r-- 1 <USER> <GROUP>      10074 May 28 11:46 .nextflow.log
  -rw-rw-r-- 1 <USER> <GROUP>      22994 May 28 10:26 .nextflow.log.1
  -rw-rw-r-- 1 <USER> <GROUP>      95895 May 27 16:00 .nextflow.log.2
  -rw-rw-r-- 1 <USER> <GROUP>    4190374 May 19 05:48 GCF_008369605.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>    4809616 Oct 26  2022 GCF_025823245.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>       4872 May 19 22:26 README.md
  -rw-rw-r-- 1 <USER> <GROUP>      12855 May 20 12:42 autoencoder_tool.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 09:22 backup
  drwxrwsr-x 2 laureli nogroup       4096 May 22 04:29 backup_parallel
  -rw-rw-r-- 1 <USER> <GROUP>     487562 May 20 13:56 best_refined_model_RS.pkl
  drwxrwsr-x 2 laureli nogroup       4096 May 19 07:56 bin
  -rwxrwxr-x 1 <USER> <GROUP>        712 May 19 07:56 build_containers.sh
  -rw-rw-r-- 1 <USER> <GROUP>       5569 May 20 12:19 check_compatibility.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 08:55 containers
  -rw-rw-r-- 1 <USER> <GROUP>       8199 May 19 05:37 convert_hits.py
  -rw-rw-r-- 1 <USER> <GROUP>       9583 May 19 05:31 create_unique_df_hits_optimized.py
  -rw-rw-r-- 1 <USER> <GROUP>       5846 May 20 12:19 encode_final_results.py
  -rwxrwxr-x 1 <USER> <GROUP>       1612 May 21 02:28 fix_workflow.sh
  -rwxrwxr-x 1 <USER> <GROUP>       4552 May 19 05:30 hmmsearch_python.py
  -rw-rw-r-- 1 <USER> <GROUP>      12303 May 27 11:26 main.nf
  -rw-rw-r-- 1 <USER> <GROUP>      10864 May 21 09:56 main.nf.fixed
  drwxrwsr-x 2 laureli nogroup       4096 May 27 08:23 ml
  -rw-rw-r-- 1 <USER> <GROUP>       9532 May 20 13:56 model_load_and_predict.py
  -rw-rw-r-- 1 <USER> <GROUP>       1922 May 27 10:42 nextflow.config
  drwxrwsr-x 2 laureli nogroup       4096 May 22 05:06 ppred
  -rwxrwxr-x 1 <USER> <GROUP>       1741 May 20 10:39 process_existing_results.sh
  -rw-rw-r-- 1 <USER> <GROUP>       2739 May 20 10:31 process_results.py
  -rw-rw-r-- 1 <USER> <GROUP>       1231 May 19 07:11 project_summary.txt
  -rw-rw-r-- 1 <USER> <GROUP>         22 May 28 01:52 remove.log
  -rw-rw-r-- 1 <USER> <GROUP>    2944023 May 27 09:15 report-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 results
  -rw-rw-r-- 1 <USER> <GROUP> 9698149560 May 20 12:44 robustscaler_enc1024_layers1.h5
  -rw-rw-r-- 1 <USER> <GROUP>    1171829 May 20 12:43 robustscaler_enc1024_layers1.pkl
  -rw-rw-r-- 1 <USER> <GROUP>       2720 May 20 10:34 run_final_step.py
  -rwxrwxr-x 1 <USER> <GROUP>      11673 May 19 05:35 run_gtdbtk.py
  -rwxrwxr-x 1 <USER> <GROUP>       6093 May 19 05:29 run_prodigal_and_rename.py
  -rw-rw-r-- 1 <USER> <GROUP>        261 May 19 23:33 test_path.nf
  -rw-rw-r-- 1 <USER> <GROUP>     252307 May 27 09:15 timeline-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 11:46 work

Work dir:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/******************************

Container:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/containers/hmmer.sif

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
May-28 11:46:42.706 [TaskFinalizer-1] DEBUG nextflow.Session - Session aborted -- Cause: Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)
May-28 11:46:42.717 [TaskFinalizer-1] DEBUG nextflow.Session - The following nodes are still active:
[process] HMMSEARCH
  status=ACTIVE
  port 0: (value) OPEN  ; channel: __$eachinparam<0>
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (value) bound ; channel: __$eachinparam<2>
  port 3: (cntrl) -     ; channel: $

[process] PROCESS_HITS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (cntrl) -     ; channel: $

May-28 11:46:42.719 [main] DEBUG nextflow.Session - Session await > all processes finished
May-28 11:46:42.720 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-28 11:46:42.720 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-28 11:46:42.728 [main] WARN  n.processor.TaskPollingMonitor - Killing running tasks (1)
May-28 11:46:42.738 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=1; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=1; succeedDuration=0ms; failedDuration=1.9s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=2; peakCpus=5; peakMemory=12 GB; ]
May-28 11:46:42.937 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-28 11:46:42.955 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
