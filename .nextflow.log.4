Jun-01 18:11:49.452 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
Jun-01 18:11:49.649 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
Jun-01 18:11:49.675 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
Jun-01 18:11:49.731 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-01 18:11:49.734 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-01 18:11:49.737 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-01 18:11:49.762 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-01 18:11:49.782 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-01 18:11:49.797 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-01 18:11:49.836 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
Jun-01 18:11:49.840 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@9f6e406] - activable => nextflow.secret.LocalSecretsProvider@9f6e406
Jun-01 18:11:49.852 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Jun-01 18:11:50.386 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-01 18:11:50.414 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [hungry_celsius] DSL2 - revision: e9019381d3
Jun-01 18:11:50.416 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-01 18:11:50.416 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-01 18:11:50.474 [main] DEBUG nextflow.Session - Session UUID: 32b97d0d-7f31-479d-8555-e9c5ee892fb7
Jun-01 18:11:50.475 [main] DEBUG nextflow.Session - Run name: hungry_celsius
Jun-01 18:11:50.477 [main] DEBUG nextflow.Session - Executor pool size: 32
Jun-01 18:11:50.490 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-01 18:11:50.495 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 18:11:50.574 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (117.6 GB) - Swap: 8 GB (468 KB)
Jun-01 18:11:50.627 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
Jun-01 18:11:50.671 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-01 18:11:50.689 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-01 18:11:50.693 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-01 18:11:50.719 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-01 18:11:50.729 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
Jun-01 18:11:50.984 [main] DEBUG nextflow.Session - Session start
Jun-01 18:11:51.655 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-01 18:11:51.693 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

Jun-01 18:11:51.803 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
Jun-01 18:11:51.813 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 18:11:51.814 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 18:11:51.821 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-01 18:11:51.829 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
Jun-01 18:11:51.832 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-01 18:11:51.859 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
Jun-01 18:11:51.919 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
Jun-01 18:11:51.937 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 18:11:51.937 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 18:11:51.938 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
Jun-01 18:11:51.940 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
Jun-01 18:11:51.952 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
Jun-01 18:11:51.954 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 18:11:51.960 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 18:11:51.969 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
Jun-01 18:11:51.972 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
Jun-01 18:11:51.994 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
Jun-01 18:11:51.996 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 18:11:51.996 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 18:11:52.011 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
Jun-01 18:11:52.024 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 18:11:52.024 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 18:11:52.025 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
Jun-01 18:11:52.033 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 18:11:52.033 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 18:11:52.036 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
Jun-01 18:11:52.040 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
Jun-01 18:11:52.042 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
Jun-01 18:11:52.051 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
Jun-01 18:11:52.051 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-01 18:11:52.054 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-01 18:11:52.056 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-01 18:11:52.057 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-01 18:11:52.068 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
Jun-01 18:11:52.069 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
Jun-01 18:11:52.071 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
Jun-01 18:11:52.072 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_1343ce0d68727479: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Jun-01 18:11:52.073 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-01 18:11:52.074 [main] DEBUG nextflow.Session - Session await
Jun-01 18:11:52.215 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:11:52.217 [Task submitter] INFO  nextflow.Session - [60/3ce71f] Submitted process > PRODIGAL (1)
Jun-01 18:11:59.803 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/60/3ce71fcc58695fd71714baa30814d3]
Jun-01 18:11:59.804 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 18:11:59.830 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 18:11:59.908 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:11:59.908 [Task submitter] INFO  nextflow.Session - [e5/6dd8c0] Submitted process > HMMSEARCH (batch_17)
Jun-01 18:11:59.937 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:11:59.937 [Task submitter] INFO  nextflow.Session - [1f/f6c46b] Submitted process > HMMSEARCH (batch_19)
Jun-01 18:12:07.165 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/6dd8c0bb8ef1b51d758d95f70fe58e]
Jun-01 18:12:07.185 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:07.185 [Task submitter] INFO  nextflow.Session - [57/af7199] Submitted process > HMMSEARCH (batch_13)
Jun-01 18:12:07.574 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1f/f6c46b5c258397bd99d9e47005862e]
Jun-01 18:12:07.604 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:07.605 [Task submitter] INFO  nextflow.Session - [b0/ce3e6e] Submitted process > HMMSEARCH (batch_1)
Jun-01 18:12:14.532 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/af7199dff33e787789c19bf05a451a]
Jun-01 18:12:14.547 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:14.547 [Task submitter] INFO  nextflow.Session - [1b/5e9d97] Submitted process > HMMSEARCH (batch_30)
Jun-01 18:12:14.745 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b0/ce3e6ebcfce13bade90c96a962fd77]
Jun-01 18:12:14.758 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:14.759 [Task submitter] INFO  nextflow.Session - [71/c2453f] Submitted process > HMMSEARCH (batch_27)
Jun-01 18:12:21.398 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1b/5e9d9745033720c37136b36659b0e2]
Jun-01 18:12:21.418 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:21.419 [Task submitter] INFO  nextflow.Session - [1c/4fbfa2] Submitted process > HMMSEARCH (batch_22)
Jun-01 18:12:21.603 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/c2453f197d3ec9c9f7de7b7cf312bf]
Jun-01 18:12:21.617 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:21.618 [Task submitter] INFO  nextflow.Session - [07/e20101] Submitted process > HMMSEARCH (batch_31)
Jun-01 18:12:28.045 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/4fbfa2dcfadb0895c9b535e47dae6b]
Jun-01 18:12:28.055 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:28.056 [Task submitter] INFO  nextflow.Session - [71/7d4370] Submitted process > HMMSEARCH (batch_16)
Jun-01 18:12:28.135 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/07/e201016cd1b94ae8952b2627d87f2d]
Jun-01 18:12:28.157 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:28.158 [Task submitter] INFO  nextflow.Session - [17/5433ef] Submitted process > HMMSEARCH (batch_11)
Jun-01 18:12:35.413 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/7d4370a108ddcf4ab191bfbab16efb]
Jun-01 18:12:35.435 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:35.435 [Task submitter] INFO  nextflow.Session - [6e/d8edfc] Submitted process > HMMSEARCH (batch_9)
Jun-01 18:12:35.473 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/5433ef787ce2fb86dc1331e95bcf5c]
Jun-01 18:12:35.509 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:35.509 [Task submitter] INFO  nextflow.Session - [94/f006c6] Submitted process > HMMSEARCH (batch_32)
Jun-01 18:12:43.076 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6e/d8edfc9ce8701b2eae99a076a127a3]
Jun-01 18:12:43.102 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:43.103 [Task submitter] INFO  nextflow.Session - [a6/bf957f] Submitted process > HMMSEARCH (batch_26)
Jun-01 18:12:43.165 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/94/f006c6bceaf37e85a1a25b6f3df7eb]
Jun-01 18:12:43.196 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:43.196 [Task submitter] INFO  nextflow.Session - [c6/02efe2] Submitted process > HMMSEARCH (batch_6)
Jun-01 18:12:50.803 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a6/bf957fffa3405d1436c4e091e5f655]
Jun-01 18:12:50.823 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:50.824 [Task submitter] INFO  nextflow.Session - [33/cbae27] Submitted process > HMMSEARCH (batch_10)
Jun-01 18:12:51.145 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/02efe299825b5f833f0d79273b9a45]
Jun-01 18:12:51.160 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:51.160 [Task submitter] INFO  nextflow.Session - [3e/099302] Submitted process > HMMSEARCH (batch_14)
Jun-01 18:12:58.196 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/33/cbae27c3bd5b0ce613c59a9077c962]
Jun-01 18:12:58.211 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:58.212 [Task submitter] INFO  nextflow.Session - [41/d45803] Submitted process > HMMSEARCH (batch_7)
Jun-01 18:12:58.769 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3e/0993022974922ab2fe44ebc8524f9b]
Jun-01 18:12:58.787 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:12:58.787 [Task submitter] INFO  nextflow.Session - [37/e90a16] Submitted process > HMMSEARCH (batch_5)
Jun-01 18:13:05.362 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/41/d45803135d29497b7832cabed60afc]
Jun-01 18:13:05.375 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:05.376 [Task submitter] INFO  nextflow.Session - [66/3ef440] Submitted process > HMMSEARCH (batch_2)
Jun-01 18:13:05.920 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/37/e90a168e3ddb26a7a0753e167fef19]
Jun-01 18:13:05.942 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:05.943 [Task submitter] INFO  nextflow.Session - [41/4b6a13] Submitted process > HMMSEARCH (batch_23)
Jun-01 18:13:12.631 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/66/3ef440d388f5f80c4400e3aa5a8a7c]
Jun-01 18:13:12.645 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:12.646 [Task submitter] INFO  nextflow.Session - [7f/359e5d] Submitted process > HMMSEARCH (batch_21)
Jun-01 18:13:13.303 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/41/4b6a13f482d6ca417f7cbd96ef0936]
Jun-01 18:13:13.336 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:13.336 [Task submitter] INFO  nextflow.Session - [c8/8f0e70] Submitted process > HMMSEARCH (batch_8)
Jun-01 18:13:19.993 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/359e5de39ec879765c3e3a21a84113]
Jun-01 18:13:20.009 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:20.010 [Task submitter] INFO  nextflow.Session - [47/fdb8d2] Submitted process > HMMSEARCH (batch_3)
Jun-01 18:13:20.568 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c8/8f0e70cdb5cee2c0219ee6579d02fb]
Jun-01 18:13:20.583 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:20.584 [Task submitter] INFO  nextflow.Session - [e1/f3daca] Submitted process > HMMSEARCH (batch_25)
Jun-01 18:13:26.736 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/fdb8d258795ffacd699867cdebe749]
Jun-01 18:13:26.748 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:26.748 [Task submitter] INFO  nextflow.Session - [32/1267d9] Submitted process > HMMSEARCH (batch_28)
Jun-01 18:13:27.250 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/f3daca440935215bc453229ebb7c91]
Jun-01 18:13:27.275 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:27.276 [Task submitter] INFO  nextflow.Session - [89/f737fa] Submitted process > HMMSEARCH (batch_18)
Jun-01 18:13:33.003 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/32/1267d962199214640bf9ac6f0b85d8]
Jun-01 18:13:33.014 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:33.014 [Task submitter] INFO  nextflow.Session - [dd/93f739] Submitted process > HMMSEARCH (batch_20)
Jun-01 18:13:33.976 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/89/f737fa64b529aeed995d97c67943aa]
Jun-01 18:13:33.994 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:33.994 [Task submitter] INFO  nextflow.Session - [09/a15b80] Submitted process > HMMSEARCH (batch_29)
Jun-01 18:13:39.932 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dd/93f73978bc02763b7f9e6a91b849d2]
Jun-01 18:13:39.949 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:39.950 [Task submitter] INFO  nextflow.Session - [bb/b519ae] Submitted process > HMMSEARCH (batch_15)
Jun-01 18:13:40.861 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/09/a15b80e9b24953858e2290418efae8]
Jun-01 18:13:40.874 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:40.874 [Task submitter] INFO  nextflow.Session - [f8/209518] Submitted process > HMMSEARCH (batch_12)
Jun-01 18:13:46.591 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bb/b519ae5e1d124ec00202b2c6be9c05]
Jun-01 18:13:46.610 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:46.610 [Task submitter] INFO  nextflow.Session - [4d/dea035] Submitted process > HMMSEARCH (batch_4)
Jun-01 18:13:47.494 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/2095187a37fd811a090c315966f48d]
Jun-01 18:13:47.520 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:47.520 [Task submitter] INFO  nextflow.Session - [c8/8c40f9] Submitted process > HMMSEARCH (batch_35)
Jun-01 18:13:53.336 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4d/dea03574395337c063c0f54ba46a8d]
Jun-01 18:13:53.349 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:53.349 [Task submitter] INFO  nextflow.Session - [9b/cd449b] Submitted process > HMMSEARCH (batch_24)
Jun-01 18:13:54.101 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c8/8c40f9733652aff9b1a2dee661c1a1]
Jun-01 18:13:54.116 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:13:54.116 [Task submitter] INFO  nextflow.Session - [68/bd92c5] Submitted process > HMMSEARCH (batch_37)
Jun-01 18:14:00.673 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9b/cd449bffaaad32e7ac94c22be8a8f7]
Jun-01 18:14:00.691 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:00.692 [Task submitter] INFO  nextflow.Session - [29/b21e02] Submitted process > HMMSEARCH (batch_58)
Jun-01 18:14:01.002 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/bd92c5eced8c8981d042e93d69871f]
Jun-01 18:14:01.013 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:01.013 [Task submitter] INFO  nextflow.Session - [f6/b71903] Submitted process > HMMSEARCH (batch_33)
Jun-01 18:14:07.651 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/b21e028c359289d4939be66146fb7f]
Jun-01 18:14:07.691 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:07.691 [Task submitter] INFO  nextflow.Session - [f2/9dcbec] Submitted process > HMMSEARCH (batch_47)
Jun-01 18:14:07.952 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f6/b719039f0fb284166f7297df51e349]
Jun-01 18:14:07.962 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:07.962 [Task submitter] INFO  nextflow.Session - [5a/f288ca] Submitted process > HMMSEARCH (batch_61)
Jun-01 18:14:14.547 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f2/9dcbecff61772b41cce9207c3436e6]
Jun-01 18:14:14.561 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:14.561 [Task submitter] INFO  nextflow.Session - [40/f39a17] Submitted process > HMMSEARCH (batch_48)
Jun-01 18:14:14.795 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/f288ca88367601e0f8c003bf7cb4bb]
Jun-01 18:14:14.812 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:14.813 [Task submitter] INFO  nextflow.Session - [f1/04919a] Submitted process > HMMSEARCH (batch_42)
Jun-01 18:14:21.460 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/f39a1772d718886d12518fbecb3a67]
Jun-01 18:14:21.472 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:21.472 [Task submitter] INFO  nextflow.Session - [6b/53e87e] Submitted process > HMMSEARCH (batch_44)
Jun-01 18:14:21.627 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/04919a08c1f77a39f080fec66a3d85]
Jun-01 18:14:21.652 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:21.652 [Task submitter] INFO  nextflow.Session - [a1/e5cf1a] Submitted process > HMMSEARCH (batch_46)
Jun-01 18:14:28.346 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/53e87e41e700be5acdc2fc27172214]
Jun-01 18:14:28.367 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:28.367 [Task submitter] INFO  nextflow.Session - [49/483183] Submitted process > HMMSEARCH (batch_53)
Jun-01 18:14:28.402 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/e5cf1a5304cd8740d711efc564e242]
Jun-01 18:14:28.423 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:28.423 [Task submitter] INFO  nextflow.Session - [42/27bcfa] Submitted process > HMMSEARCH (batch_52)
Jun-01 18:14:34.996 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/49/483183b1e4831ce2e065ef8c6c540d]
Jun-01 18:14:35.012 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:35.013 [Task submitter] INFO  nextflow.Session - [27/fa000d] Submitted process > HMMSEARCH (batch_55)
Jun-01 18:14:35.202 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/27bcfa5c8c630aad02ae2f95b44fbc]
Jun-01 18:14:35.215 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:35.215 [Task submitter] INFO  nextflow.Session - [c7/dd7a92] Submitted process > HMMSEARCH (batch_34)
Jun-01 18:14:41.625 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/27/fa000d0e73d824298e7924dbb76068]
Jun-01 18:14:41.642 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:41.643 [Task submitter] INFO  nextflow.Session - [00/9581a6] Submitted process > HMMSEARCH (batch_51)
Jun-01 18:14:41.993 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c7/dd7a92ff5a3f31061bff95ef18d8bd]
Jun-01 18:14:42.007 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:42.007 [Task submitter] INFO  nextflow.Session - [85/bcd991] Submitted process > HMMSEARCH (batch_60)
Jun-01 18:14:48.271 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/00/9581a66d1bac6f62e993879f0c497b]
Jun-01 18:14:48.308 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:48.308 [Task submitter] INFO  nextflow.Session - [49/fafe10] Submitted process > HMMSEARCH (batch_38)
Jun-01 18:14:48.790 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/85/bcd9915878291b4df0a326006fb209]
Jun-01 18:14:48.805 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:48.806 [Task submitter] INFO  nextflow.Session - [0d/2b2319] Submitted process > HMMSEARCH (batch_57)
Jun-01 18:14:55.989 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/49/fafe10c207c5dd7a4084a904d8a453]
Jun-01 18:14:56.008 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:56.008 [Task submitter] INFO  nextflow.Session - [ea/e28e7e] Submitted process > HMMSEARCH (batch_59)
Jun-01 18:14:56.409 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0d/2b2319607570575fe71779d49e63c4]
Jun-01 18:14:56.426 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:14:56.426 [Task submitter] INFO  nextflow.Session - [08/392c6d] Submitted process > HMMSEARCH (batch_41)
Jun-01 18:15:03.283 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ea/e28e7e1556393bb595be7aa09b169d]
Jun-01 18:15:03.293 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:03.294 [Task submitter] INFO  nextflow.Session - [a7/ec0426] Submitted process > HMMSEARCH (batch_40)
Jun-01 18:15:03.615 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/392c6dfb3c69ee59674d72635bbc84]
Jun-01 18:15:03.638 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:03.639 [Task submitter] INFO  nextflow.Session - [bd/e92336] Submitted process > HMMSEARCH (batch_56)
Jun-01 18:15:10.840 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a7/ec04262dba931ff13c71253f74f31e]
Jun-01 18:15:10.866 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:10.867 [Task submitter] INFO  nextflow.Session - [0a/7037af] Submitted process > HMMSEARCH (batch_43)
Jun-01 18:15:11.201 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/e92336efb042d0c5e5c8dda84e7658]
Jun-01 18:15:11.222 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:11.222 [Task submitter] INFO  nextflow.Session - [bd/9ad013] Submitted process > HMMSEARCH (batch_39)
Jun-01 18:15:18.263 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/7037af7be197b544cd4bf1a3427a21]
Jun-01 18:15:18.276 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:18.277 [Task submitter] INFO  nextflow.Session - [0a/e50753] Submitted process > HMMSEARCH (batch_36)
Jun-01 18:15:18.875 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/9ad013ddbe4175f5c5c71210a210fa]
Jun-01 18:15:18.901 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:18.901 [Task submitter] INFO  nextflow.Session - [65/ba490a] Submitted process > HMMSEARCH (batch_50)
Jun-01 18:15:25.964 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/e50753fd45e629819e8bd71e240265]
Jun-01 18:15:25.992 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:25.993 [Task submitter] INFO  nextflow.Session - [40/6b37e0] Submitted process > HMMSEARCH (batch_62)
Jun-01 18:15:26.625 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/65/ba490aaa071f2e4da025cabc544540]
Jun-01 18:15:26.651 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:26.651 [Task submitter] INFO  nextflow.Session - [54/0c4351] Submitted process > HMMSEARCH (batch_49)
Jun-01 18:15:33.261 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/6b37e054d74fe8f8cf7a85221286c8]
Jun-01 18:15:33.291 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:33.291 [Task submitter] INFO  nextflow.Session - [bd/2486fa] Submitted process > HMMSEARCH (batch_54)
Jun-01 18:15:33.427 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/54/0c43510b190bb531f921d3f6e2c256]
Jun-01 18:15:33.453 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:33.454 [Task submitter] INFO  nextflow.Session - [11/312e05] Submitted process > HMMSEARCH (batch_45)
Jun-01 18:15:40.365 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/2486fa06ee34c96244754d9d492049]
Jun-01 18:15:40.394 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:40.395 [Task submitter] INFO  nextflow.Session - [0a/fe090a] Submitted process > HMMSEARCH (batch_64)
Jun-01 18:15:40.835 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/11/312e05ef206fd73f6a0ca207cf66f2]
Jun-01 18:15:40.850 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:40.850 [Task submitter] INFO  nextflow.Session - [b2/99ce72] Submitted process > HMMSEARCH (batch_66)
Jun-01 18:15:41.877 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/99ce7231fce0cbf938ce7a5a4f9353]
Jun-01 18:15:41.899 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:41.900 [Task submitter] INFO  nextflow.Session - [8f/055bcc] Submitted process > HMMSEARCH (batch_65)
Jun-01 18:15:48.007 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/fe090ad7bbb26dcff3b3405a28f134]
Jun-01 18:15:48.023 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:48.024 [Task submitter] INFO  nextflow.Session - [f3/8ebbcc] Submitted process > HMMSEARCH (batch_63)
Jun-01 18:15:49.482 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8f/055bcc1de16a2f48e5ac8913b5d20c]
Jun-01 18:15:49.497 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.498 [Task submitter] INFO  nextflow.Session - [4a/4d1ee8] Submitted process > PROCESS_HITS (batch_17)
Jun-01 18:15:49.548 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.549 [Task submitter] INFO  nextflow.Session - [dd/9bf9d3] Submitted process > PROCESS_HITS (batch_19)
Jun-01 18:15:49.566 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.566 [Task submitter] INFO  nextflow.Session - [73/34c9fc] Submitted process > PROCESS_HITS (batch_13)
Jun-01 18:15:49.578 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.578 [Task submitter] INFO  nextflow.Session - [f2/0f9a34] Submitted process > PROCESS_HITS (batch_1)
Jun-01 18:15:49.595 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.595 [Task submitter] INFO  nextflow.Session - [23/7abe09] Submitted process > PROCESS_HITS (batch_30)
Jun-01 18:15:49.607 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.607 [Task submitter] INFO  nextflow.Session - [fa/e2ed60] Submitted process > PROCESS_HITS (batch_27)
Jun-01 18:15:49.631 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.631 [Task submitter] INFO  nextflow.Session - [c7/c9a886] Submitted process > PROCESS_HITS (batch_22)
Jun-01 18:15:49.654 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:49.655 [Task submitter] INFO  nextflow.Session - [75/bf2607] Submitted process > PROCESS_HITS (batch_31)
Jun-01 18:15:55.797 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f3/8ebbcc3ca964c5ac44521fa2270f04]
Jun-01 18:15:55.818 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.819 [Task submitter] INFO  nextflow.Session - [2a/4b3743] Submitted process > PROCESS_HITS (batch_16)
Jun-01 18:15:55.857 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.857 [Task submitter] INFO  nextflow.Session - [0e/7ce886] Submitted process > PROCESS_HITS (batch_11)
Jun-01 18:15:55.872 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.873 [Task submitter] INFO  nextflow.Session - [48/7b1011] Submitted process > PROCESS_HITS (batch_9)
Jun-01 18:15:55.890 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.890 [Task submitter] INFO  nextflow.Session - [32/52ca45] Submitted process > PROCESS_HITS (batch_32)
Jun-01 18:15:55.920 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.920 [Task submitter] INFO  nextflow.Session - [e0/599c25] Submitted process > PROCESS_HITS (batch_26)
Jun-01 18:15:55.935 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.935 [Task submitter] INFO  nextflow.Session - [29/2bd74e] Submitted process > PROCESS_HITS (batch_6)
Jun-01 18:15:55.969 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.969 [Task submitter] INFO  nextflow.Session - [3c/1bc5c6] Submitted process > PROCESS_HITS (batch_10)
Jun-01 18:15:55.983 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:15:55.985 [Task submitter] INFO  nextflow.Session - [0a/43ae45] Submitted process > PROCESS_HITS (batch_14)
Jun-01 18:16:04.752 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fa/e2ed60bb9fb6ff2f5e544f85ca6797]
Jun-01 18:16:04.775 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:04.776 [Task submitter] INFO  nextflow.Session - [59/c7f222] Submitted process > PROCESS_HITS (batch_7)
Jun-01 18:16:04.975 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/23/7abe09a730cfe6ff640e69bb902e8a]
Jun-01 18:16:05.013 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:05.013 [Task submitter] INFO  nextflow.Session - [db/2bc897] Submitted process > PROCESS_HITS (batch_5)
Jun-01 18:16:05.288 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dd/9bf9d3c71aa760f81891d3f1b6939e]
Jun-01 18:16:05.319 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:05.319 [Task submitter] INFO  nextflow.Session - [06/584e48] Submitted process > PROCESS_HITS (batch_2)
Jun-01 18:16:05.572 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/75/bf26078bf2a3a10726d1ec98b209bc]
Jun-01 18:16:05.587 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/73/34c9fc8c8b16ec7636bd3d0d790213]
Jun-01 18:16:05.594 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4a/4d1ee8530ddfce79a0fe8a347ab7b2]
Jun-01 18:16:05.604 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:05.604 [Task submitter] INFO  nextflow.Session - [b8/36744b] Submitted process > PROCESS_HITS (batch_23)
Jun-01 18:16:05.633 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:05.633 [Task submitter] INFO  nextflow.Session - [c8/747757] Submitted process > PROCESS_HITS (batch_21)
Jun-01 18:16:05.665 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:05.665 [Task submitter] INFO  nextflow.Session - [ac/4fb7c7] Submitted process > PROCESS_HITS (batch_8)
Jun-01 18:16:05.667 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f2/0f9a34d746ee88e2b34854ca69bbce]
Jun-01 18:16:05.698 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:05.699 [Task submitter] INFO  nextflow.Session - [0a/90bf5d] Submitted process > PROCESS_HITS (batch_3)
Jun-01 18:16:05.798 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c7/c9a886fde1b2edd91b294854a6a1af]
Jun-01 18:16:05.830 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:05.830 [Task submitter] INFO  nextflow.Session - [f2/8cdd2a] Submitted process > PROCESS_HITS (batch_25)
Jun-01 18:16:10.621 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/2bd74eb5f7e1b88bb3dc6d00e3fec6]
Jun-01 18:16:10.646 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:10.646 [Task submitter] INFO  nextflow.Session - [f4/bbf4a3] Submitted process > PROCESS_HITS (batch_28)
Jun-01 18:16:10.705 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/48/7b1011b3b3117d1b645379a50409e3]
Jun-01 18:16:10.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:10.729 [Task submitter] INFO  nextflow.Session - [29/2d708f] Submitted process > PROCESS_HITS (batch_18)
Jun-01 18:16:10.884 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0e/7ce886fcecf96d415e6f1192645b70]
Jun-01 18:16:10.904 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:10.904 [Task submitter] INFO  nextflow.Session - [53/edc7ba] Submitted process > PROCESS_HITS (batch_20)
Jun-01 18:16:11.060 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3c/1bc5c691e77d9f85233fba5963ce8a]
Jun-01 18:16:11.080 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:11.081 [Task submitter] INFO  nextflow.Session - [84/38d435] Submitted process > PROCESS_HITS (batch_29)
Jun-01 18:16:11.172 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/32/52ca458231bed05be185864bd9f2a6]
Jun-01 18:16:11.188 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:11.188 [Task submitter] INFO  nextflow.Session - [0e/578d30] Submitted process > PROCESS_HITS (batch_15)
Jun-01 18:16:11.231 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/4b374324977bab50742a1640ba6f6a]
Jun-01 18:16:11.244 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/43ae459f591d4767d7a48259400aa0]
Jun-01 18:16:11.248 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:11.249 [Task submitter] INFO  nextflow.Session - [4f/9524ea] Submitted process > PROCESS_HITS (batch_12)
Jun-01 18:16:11.284 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:11.285 [Task submitter] INFO  nextflow.Session - [2b/232d5f] Submitted process > PROCESS_HITS (batch_4)
Jun-01 18:16:11.835 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e0/599c2523eaa6a3d39a119dfb35f89c]
Jun-01 18:16:11.855 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:11.855 [Task submitter] INFO  nextflow.Session - [ae/b9bc3e] Submitted process > PROCESS_HITS (batch_35)
Jun-01 18:16:19.208 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/db/2bc8976510c2df7bcf533ea860ba4a]
Jun-01 18:16:19.209 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/59/c7f222df4b4a8d499c601c8dc0c61f]
Jun-01 18:16:19.233 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:19.234 [Task submitter] INFO  nextflow.Session - [f6/e22b65] Submitted process > PROCESS_HITS (batch_24)
Jun-01 18:16:19.253 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:19.253 [Task submitter] INFO  nextflow.Session - [cc/bb1a44] Submitted process > PROCESS_HITS (batch_37)
Jun-01 18:16:19.633 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/06/584e4880b66005e44d9f0d6b76cfbd]
Jun-01 18:16:19.647 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:19.648 [Task submitter] INFO  nextflow.Session - [11/b7e233] Submitted process > PROCESS_HITS (batch_58)
Jun-01 18:16:19.826 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c8/7477577047d7c120fa25a8ede14bcd]
Jun-01 18:16:19.843 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:19.843 [Task submitter] INFO  nextflow.Session - [1d/89eaff] Submitted process > PROCESS_HITS (batch_33)
Jun-01 18:16:19.880 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/90bf5d4ee84c1db7f25c3d7c9c7e83]
Jun-01 18:16:19.918 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:19.918 [Task submitter] INFO  nextflow.Session - [47/62ce90] Submitted process > PROCESS_HITS (batch_47)
Jun-01 18:16:20.313 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ac/4fb7c73602adf8c2207e3e75c9f14b]
Jun-01 18:16:20.330 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:20.331 [Task submitter] INFO  nextflow.Session - [3f/3ec736] Submitted process > PROCESS_HITS (batch_61)
Jun-01 18:16:20.543 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f2/8cdd2a46addf5abb23595611ffe29f]
Jun-01 18:16:20.572 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:20.572 [Task submitter] INFO  nextflow.Session - [e9/844c43] Submitted process > PROCESS_HITS (batch_48)
Jun-01 18:16:21.009 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b8/36744bbed753b5afb7f8253bb1dd01]
Jun-01 18:16:21.028 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:21.028 [Task submitter] INFO  nextflow.Session - [68/932b87] Submitted process > PROCESS_HITS (batch_42)
Jun-01 18:16:24.853 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f4/bbf4a3b178daefeebd18414ac8d4a3]
Jun-01 18:16:24.871 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:24.871 [Task submitter] INFO  nextflow.Session - [7c/7f2544] Submitted process > PROCESS_HITS (batch_44)
Jun-01 18:16:25.064 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/53/edc7bae15eb4a50ecbe6aec6a8d693]
Jun-01 18:16:25.083 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:25.084 [Task submitter] INFO  nextflow.Session - [b4/ab6a3c] Submitted process > PROCESS_HITS (batch_46)
Jun-01 18:16:25.168 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0e/578d3081342942c43bd1fc0e5f5516]
Jun-01 18:16:25.217 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:25.217 [Task submitter] INFO  nextflow.Session - [75/f8901c] Submitted process > PROCESS_HITS (batch_53)
Jun-01 18:16:25.391 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/2d708f95fa58a21f13c18cb6bb75f4]
Jun-01 18:16:25.413 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:25.413 [Task submitter] INFO  nextflow.Session - [d2/d30a90] Submitted process > PROCESS_HITS (batch_52)
Jun-01 18:16:25.495 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/232d5f06c0ba7e73f903a28cbc34d5]
Jun-01 18:16:25.523 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:25.524 [Task submitter] INFO  nextflow.Session - [15/46622c] Submitted process > PROCESS_HITS (batch_55)
Jun-01 18:16:25.525 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/38d435de26a48517f0f816f7a9dddb]
Jun-01 18:16:25.547 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:25.548 [Task submitter] INFO  nextflow.Session - [63/200dcb] Submitted process > PROCESS_HITS (batch_34)
Jun-01 18:16:25.703 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ae/b9bc3e24a9165dc40b8410eaac48e3]
Jun-01 18:16:25.729 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:25.729 [Task submitter] INFO  nextflow.Session - [67/8179cf] Submitted process > PROCESS_HITS (batch_51)
Jun-01 18:16:26.018 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4f/9524eaf60efe50853ac6d7f6587319]
Jun-01 18:16:26.034 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:26.035 [Task submitter] INFO  nextflow.Session - [ed/ff4349] Submitted process > PROCESS_HITS (batch_60)
Jun-01 18:16:32.811 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f6/e22b654dee9fa3de54a6748f496d70]
Jun-01 18:16:32.838 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:32.838 [Task submitter] INFO  nextflow.Session - [a9/557323] Submitted process > PROCESS_HITS (batch_38)
Jun-01 18:16:33.053 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cc/bb1a444f6b5efc3efd55a4aca766c6]
Jun-01 18:16:33.076 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:33.077 [Task submitter] INFO  nextflow.Session - [fa/d9279f] Submitted process > PROCESS_HITS (batch_57)
Jun-01 18:16:33.377 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1d/89eaff2651fd97ab1235469c2ad804]
Jun-01 18:16:33.394 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:33.394 [Task submitter] INFO  nextflow.Session - [57/12dc16] Submitted process > PROCESS_HITS (batch_59)
Jun-01 18:16:33.643 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/62ce909d1c7499f1771db031a457b9]
Jun-01 18:16:33.662 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:33.663 [Task submitter] INFO  nextflow.Session - [39/d12056] Submitted process > PROCESS_HITS (batch_41)
Jun-01 18:16:33.759 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/11/b7e2333ae888fd3dae6c1c445d4cf3]
Jun-01 18:16:33.776 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:33.776 [Task submitter] INFO  nextflow.Session - [71/2a4c7d] Submitted process > PROCESS_HITS (batch_40)
Jun-01 18:16:34.217 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3f/3ec73643820429c08e856ce0480cdf]
Jun-01 18:16:34.241 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:34.242 [Task submitter] INFO  nextflow.Session - [95/fb0cfc] Submitted process > PROCESS_HITS (batch_56)
Jun-01 18:16:34.532 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e9/844c43eeb060294f25ba69701547c7]
Jun-01 18:16:34.550 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:34.550 [Task submitter] INFO  nextflow.Session - [61/805e68] Submitted process > PROCESS_HITS (batch_43)
Jun-01 18:16:35.541 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/932b87ef55c1f6dd6bff32d343099b]
Jun-01 18:16:35.564 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:35.564 [Task submitter] INFO  nextflow.Session - [4a/c8a006] Submitted process > PROCESS_HITS (batch_39)
Jun-01 18:16:39.864 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7c/7f2544d19fc0ce0d5bb9c09d9bf717]
Jun-01 18:16:39.892 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:39.892 [Task submitter] INFO  nextflow.Session - [52/4d52f6] Submitted process > PROCESS_HITS (batch_36)
Jun-01 18:16:40.251 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/75/f8901cb9765c8bea5e28c032de64cb]
Jun-01 18:16:40.291 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:40.291 [Task submitter] INFO  nextflow.Session - [af/318434] Submitted process > PROCESS_HITS (batch_50)
Jun-01 18:16:40.704 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b4/ab6a3c1f0b18bda3f977a7879acf17]
Jun-01 18:16:40.732 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:40.732 [Task submitter] INFO  nextflow.Session - [9d/e08351] Submitted process > PROCESS_HITS (batch_62)
Jun-01 18:16:40.846 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/46622c2741dbaa593edfd63d4c9bd0]
Jun-01 18:16:40.876 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:40.876 [Task submitter] INFO  nextflow.Session - [5d/51d6dd] Submitted process > PROCESS_HITS (batch_49)
Jun-01 18:16:40.985 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/200dcb29e253a1991489035903014a]
Jun-01 18:16:41.049 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:41.049 [Task submitter] INFO  nextflow.Session - [08/ae36fa] Submitted process > PROCESS_HITS (batch_54)
Jun-01 18:16:41.067 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d2/d30a90cf9b7d2415905398f036bc2c]
Jun-01 18:16:41.095 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:41.095 [Task submitter] INFO  nextflow.Session - [b6/3395b5] Submitted process > PROCESS_HITS (batch_45)
Jun-01 18:16:41.183 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/67/8179cfcf13d35d372c90b1942dbea3]
Jun-01 18:16:41.244 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:41.244 [Task submitter] INFO  nextflow.Session - [21/662673] Submitted process > PROCESS_HITS (batch_66)
Jun-01 18:16:41.713 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ed/ff43491d2374547e1e61f4081eec41]
Jun-01 18:16:41.734 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:41.734 [Task submitter] INFO  nextflow.Session - [75/76534b] Submitted process > PROCESS_HITS (batch_64)
Jun-01 18:16:43.446 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/21/662673092d90e2d7a3282efa23252b]
Jun-01 18:16:43.461 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:43.461 [Task submitter] INFO  nextflow.Session - [d7/38a277] Submitted process > PROCESS_HITS (batch_65)
Jun-01 18:16:49.679 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/55732394036cc10a33946dbd7d2047]
Jun-01 18:16:49.696 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:16:49.696 [Task submitter] INFO  nextflow.Session - [41/057701] Submitted process > PROCESS_HITS (batch_63)
Jun-01 18:16:49.785 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fa/d9279f998ae2e635c58eca4914450c]
Jun-01 18:16:49.844 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/12dc1688c34650833178b741262420]
Jun-01 18:16:49.872 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/39/d12056bc1a802cafd19faa952a964a]
Jun-01 18:16:50.512 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/95/fb0cfc4ab9a610ee1ba2b4e7f64c74]
Jun-01 18:16:50.585 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/2a4c7dad51c6b4af7a0ba208e690f6]
Jun-01 18:16:50.935 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/805e689568df2e4862ccdb8b85af92]
Jun-01 18:16:51.921 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4a/c8a006bcc86aac4138498641601b3f]
Jun-01 18:16:52.089 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 9 -- submitted tasks are shown below
~> TaskHandler[id: 124; name: PROCESS_HITS (batch_36); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/4d52f6b22db00b35a69932028e2ffe]
~> TaskHandler[id: 125; name: PROCESS_HITS (batch_50); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/31843400088fea94bd7aef53b30cef]
~> TaskHandler[id: 126; name: PROCESS_HITS (batch_62); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9d/e083513bae189f6a74d320339f70cb]
~> TaskHandler[id: 127; name: PROCESS_HITS (batch_49); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5d/51d6dd40727cb785da7e1448fd9ecc]
~> TaskHandler[id: 128; name: PROCESS_HITS (batch_54); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/ae36fa9edde49417fd325fffb84db5]
~> TaskHandler[id: 129; name: PROCESS_HITS (batch_45); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/3395b534f73b797bd8f564de6b5682]
~> TaskHandler[id: 131; name: PROCESS_HITS (batch_64); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/75/76534bcab3784d22abd40d9b19ceec]
~> TaskHandler[id: 132; name: PROCESS_HITS (batch_65); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d7/38a27710420f24210e5696abf53ded]
~> TaskHandler[id: 133; name: PROCESS_HITS (batch_63); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/41/057701a78510a1fc31ea3e3aa4d219]
Jun-01 18:16:55.701 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/31843400088fea94bd7aef53b30cef]
Jun-01 18:16:55.733 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9d/e083513bae189f6a74d320339f70cb]
Jun-01 18:16:55.782 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/4d52f6b22db00b35a69932028e2ffe]
Jun-01 18:16:56.138 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/3395b534f73b797bd8f564de6b5682]
Jun-01 18:16:56.445 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5d/51d6dd40727cb785da7e1448fd9ecc]
Jun-01 18:16:57.186 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/ae36fa9edde49417fd325fffb84db5]
Jun-01 18:16:57.428 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/75/76534bcab3784d22abd40d9b19ceec]
Jun-01 18:16:59.113 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d7/38a27710420f24210e5696abf53ded]
Jun-01 18:17:05.431 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/41/057701a78510a1fc31ea3e3aa4d219]
Jun-01 18:19:06.127 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:19:06.128 [Task submitter] INFO  nextflow.Session - [79/7210d8] Submitted process > CREATE_DATAFRAME (1)
Jun-01 18:21:52.137 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:26:52.185 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:31:52.237 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:36:52.282 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:41:52.335 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:46:52.387 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:51:52.445 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:53:30.177 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/7210d854e159d0cc30e264d5f5d580]
Jun-01 18:53:30.210 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:53:30.211 [Task submitter] INFO  nextflow.Session - [f6/6bfec4] Submitted process > AUTOENCODER (1)
Jun-01 18:54:35.941 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: AUTOENCODER (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f6/6bfec4f37be8ee41a6d3929ee20f00]
Jun-01 18:54:35.978 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 18:54:35.979 [Task submitter] INFO  nextflow.Session - [9a/a562ea] Submitted process > MODEL_PREDICT (1)
Jun-01 18:54:37.818 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 136; name: MODEL_PREDICT (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9a/a562ea103364641207b89402e1a07e]
Jun-01 18:54:37.821 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-01 18:54:37.918 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-01 18:54:37.918 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-01 18:54:37.922 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jun-01 18:54:37.923 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jun-01 18:54:37.928 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=136; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=4h 53m 59s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=20; peakCpus=40; peakMemory=80 GB; ]
Jun-01 18:54:38.107 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-01 18:54:38.124 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-01 18:54:38.125 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
