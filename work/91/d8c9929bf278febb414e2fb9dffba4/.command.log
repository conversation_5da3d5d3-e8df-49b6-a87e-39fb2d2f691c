Running model prediction version: 1
Loading model from best_refined_model_RS.pkl...
Model loaded successfully!
Model type: <class 'xgboost.sklearn.XGBClassifier'>
Loading sample data from encoded_features.csv...
Sample data loaded successfully! Shape: (1, 1032)

Checking data compatibility...
Initial sample_df shape: (1, 1032)
Initial sample_df hash: 7780698206101728066
Sample features shape after removing metadata: (1, 1024)
Model expects 1024 features
✅ Number of features matches model expectations!
Final X_sample shape: (1, 1024)
Final X_sample hash: 2096147939457555427

Making predictions...
X_sample shape: (1, 1024)
X_sample data types: encoded_0    float64
encoded_1    float64
encoded_2    float64
encoded_3    float64
encoded_4    float64
dtype: object

First 5 rows of X_sample:
   encoded_0  encoded_1  encoded_2  encoded_3  encoded_4  ...  encoded_1019  \
0   14.58725        0.0        0.0        0.0        0.0  ...           0.0   

   encoded_1020  encoded_1021  encoded_1022  encoded_1023  
0      3.280058      3.765679           0.0           0.0  

[1 rows x 1024 columns]
X_sample data hash: 2096147939457555427
✅ Predictions saved to prediction_results.csv

Prediction Results:
            Sample  Species  Probability  Prediction           Class
0  GCF_008369605.1      NaN     0.496908           0  Non-pathogenic

Prediction process completed successfully!
