2025-06-02 14:44:13.188174: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:467] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
E0000 00:00:1748900653.205904 1378330 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
E0000 00:00:1748900653.211359 1378330 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
W0000 00:00:1748900653.225146 1378330 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1748900653.225161 1378330 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1748900653.225163 1378330 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1748900653.225165 1378330 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
2025-06-02 14:44:13.229150: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-06-02 14:45:12.004598: E external/local_xla/xla/stream_executor/cuda/cuda_platform.cc:51] failed call to cuInit: INTERNAL: CUDA error: Failed call to cuInit: CUDA_ERROR_NO_DEVICE: no CUDA-capable device is detected
2025-06-02 14:45:18.837807: E tensorflow/core/framework/node_def_util.cc:680] NodeDef mentions attribute use_unbounded_threadpool which is not in the op definition: Op<name=MapDataset; signature=input_dataset:variant, other_arguments: -> handle:variant; attr=f:func; attr=Targuments:list(type),min=0; attr=output_types:list(type),min=1; attr=output_shapes:list(shape),min=1; attr=use_inter_op_parallelism:bool,default=true; attr=preserve_cardinality:bool,default=false; attr=force_synchronous:bool,default=false; attr=metadata:string,default=""> This may be expected if your graph generating binary is newer  than this binary. Unknown attributes will be ignored. NodeDef: {{node ParallelMapDatasetV2/_14}}
Encoding data from final_results.csv using the saved autoencoder model...

=== Setting Random Seeds for Deterministic Results ===
Random seeds set: Python=42, NumPy=42, TensorFlow=42
TensorFlow deterministic operations enabled

=== Checking Compatibility ===
Checking compatibility of final_results.csv with the saved model...
The model expects 97614 input features.
Input file has 97623 total columns.
Found 8 metadata columns: ['Assembly', 'Domain', 'Phylum', 'Class', 'Order', 'Family', 'Genus', 'Species']
Note: Orthogroup138309 is present and will need to be excluded.
Found 97615 potential feature columns.
After excluding Orthogroup138309: 97614 feature columns.
✅ The number of features (97614) matches what the model expects (97614).

Reading the data to check for potential issues...
Successfully read the data. Found 1 rows.
No missing values found in the feature columns.
All feature columns have numeric data types.

✅ The file appears to be compatible with the saved model.
To use it with the model, you'll need to:
1. Exclude the Orthogroup138309 column if present
2. Fill any missing values with 0.0
3. Convert all data to float32 before scaling

=== Starting Encoding Process ===
Loading data from final_results.csv...
Successfully loaded data with shape: (1, 97623)
Extracted 8 metadata columns.
Excluded Orthogroup138309 column.
Extracted features with shape: (1, 97614)
Loading scaler from robustscaler_enc1024_layers1.pkl...
Successfully loaded scaler.
Scaled data shape: (1, 97614)
Loading model from robustscaler_enc1024_layers1.h5...
Successfully loaded model.
Found bottleneck layer at index 4 with 1024 units.
Created encoder model.
Generating encoded features...

[1m1/1[0m [32m━━━━━━━━━━━━━━━━━━━━[0m[37m[0m [1m0s[0m 123ms/step
[1m1/1[0m [32m━━━━━━━━━━━━━━━━━━━━[0m[37m[0m [1m0s[0m 135ms/step
Encoded features shape: (1, 1024)
Saving encoded features to encoded_features.csv...
Successfully saved encoded features to encoded_features.csv.

Sample of encoded features (first 5 columns):
   encoded_0  encoded_1  encoded_2  encoded_3  encoded_4
0   14.58725        0.0        0.0        0.0        0.0
