Encoding data from final_results.csv using the saved autoencoder model...

=== Setting Random Seeds for Deterministic Results ===
Random seeds set: Python=42, NumPy=42, TensorFlow=42
TensorFlow deterministic operations enabled

=== Checking Compatibility ===
Checking compatibility of final_results.csv with the saved model...
The model expects 97614 input features.
Input file has 97623 total columns.
Found 8 metadata columns: ['Assembly', 'Domain', 'Phylum', 'Class', 'Order', 'Family', 'Genus', 'Species']
Note: Orthogroup138309 is present and will need to be excluded.
Found 97615 potential feature columns.
After excluding Orthogroup138309: 97614 feature columns.
✅ The number of features (97614) matches what the model expects (97614).

Reading the data to check for potential issues...
Successfully read the data. Found 1 rows.
No missing values found in the feature columns.
All feature columns have numeric data types.

✅ The file appears to be compatible with the saved model.
To use it with the model, you'll need to:
1. Exclude the Orthogroup138309 column if present
2. Fill any missing values with 0.0
3. Convert all data to float32 before scaling

=== Starting Encoding Process ===
Loading data from final_results.csv...
Successfully loaded data with shape: (1, 97623)
Extracted 8 metadata columns.
Excluded Orthogroup138309 column.
Extracted features with shape: (1, 97614)
Loading scaler from robustscaler_enc1024_layers1.pkl...
Successfully loaded scaler.
Scaled data shape: (1, 97614)
Loading model from robustscaler_enc1024_layers1.h5...
Successfully loaded model.
Found bottleneck layer at index 4 with 1024 units.
Created encoder model.
Generating encoded features...

[1m1/1[0m [32m━━━━━━━━━━━━━━━━━━━━[0m[37m[0m [1m0s[0m 123ms/step
[1m1/1[0m [32m━━━━━━━━━━━━━━━━━━━━[0m[37m[0m [1m0s[0m 135ms/step
Encoded features shape: (1, 1024)
Saving encoded features to encoded_features.csv...
Successfully saved encoded features to encoded_features.csv.

Sample of encoded features (first 5 columns):
   encoded_0  encoded_1  encoded_2  encoded_3  encoded_4
0   14.58725        0.0        0.0        0.0        0.0
