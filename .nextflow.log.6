May-28 12:19:50.325 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
May-28 12:19:50.433 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-28 12:19:50.452 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-28 12:19:50.477 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-28 12:19:50.479 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-28 12:19:50.481 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-28 12:19:50.495 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-28 12:19:50.513 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 12:19:50.528 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 12:19:50.555 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-28 12:19:50.558 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7e97551f] - activable => nextflow.secret.LocalSecretsProvider@7e97551f
May-28 12:19:50.569 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-28 12:19:51.052 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-28 12:19:51.067 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [gloomy_perlman] DSL2 - revision: e9019381d3
May-28 12:19:51.068 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-28 12:19:51.069 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-28 12:19:51.114 [main] DEBUG nextflow.Session - Session UUID: 6c899251-41ab-48df-8e28-55c3b903a0f2
May-28 12:19:51.114 [main] DEBUG nextflow.Session - Run name: gloomy_perlman
May-28 12:19:51.115 [main] DEBUG nextflow.Session - Executor pool size: 32
May-28 12:19:51.124 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-28 12:19:51.128 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 12:19:51.147 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (4.8 GB) - Swap: 8 GB (0)
May-28 12:19:51.185 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-28 12:19:51.223 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-28 12:19:51.237 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-28 12:19:51.241 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-28 12:19:51.269 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-28 12:19:51.276 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-28 12:19:51.418 [main] DEBUG nextflow.Session - Session start
May-28 12:19:52.098 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-28 12:19:52.141 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-28 12:19:52.257 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-28 12:19:52.269 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 12:19:52.269 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 12:19:52.279 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-28 12:19:52.285 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-28 12:19:52.287 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-28 12:19:52.308 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-28 12:19:52.370 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-28 12:19:52.387 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 12:19:52.387 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 12:19:52.388 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-28 12:19:52.390 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-28 12:19:52.403 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-28 12:19:52.405 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 12:19:52.406 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 12:19:52.415 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-28 12:19:52.416 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-28 12:19:52.439 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-28 12:19:52.441 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 12:19:52.442 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 12:19:52.445 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-28 12:19:52.453 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 12:19:52.453 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 12:19:52.454 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-28 12:19:52.462 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 12:19:52.463 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 12:19:52.464 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-28 12:19:52.468 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-28 12:19:52.471 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
May-28 12:19:52.476 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-28 12:19:52.477 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 12:19:52.479 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 12:19:52.480 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 12:19:52.481 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 12:19:52.483 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-28 12:19:52.484 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-28 12:19:52.485 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-28 12:19:52.491 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_1343ce0d68727479: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-28 12:19:52.493 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-28 12:19:52.494 [main] DEBUG nextflow.Session - Session await
May-28 12:19:52.616 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:19:52.618 [Task submitter] INFO  nextflow.Session - [7a/b63913] Submitted process > PRODIGAL (1)
May-28 12:19:59.906 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7a/b639137c972cee519f2ca7662e370a]
May-28 12:19:59.908 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 12:19:59.928 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 12:19:59.991 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:19:59.991 [Task submitter] INFO  nextflow.Session - [d2/ba5377] Submitted process > HMMSEARCH (batch_10)
May-28 12:20:00.005 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:00.005 [Task submitter] INFO  nextflow.Session - [59/0797a4] Submitted process > HMMSEARCH (batch_23)
May-28 12:20:06.742 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/59/0797a4ff3d9a9eada920adb0e98bb7]
May-28 12:20:06.754 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:06.754 [Task submitter] INFO  nextflow.Session - [c9/3d5c6d] Submitted process > HMMSEARCH (batch_8)
May-28 12:20:06.786 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d2/ba5377905443ed39fdd2f87ae348a0]
May-28 12:20:06.798 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:06.798 [Task submitter] INFO  nextflow.Session - [57/ea6957] Submitted process > HMMSEARCH (batch_12)
May-28 12:20:13.701 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/3d5c6d2fd11dfadf4d67748b8028b7]
May-28 12:20:13.719 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:13.720 [Task submitter] INFO  nextflow.Session - [14/43ad94] Submitted process > HMMSEARCH (batch_13)
May-28 12:20:13.814 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/ea6957750db2c27164bb3562811064]
May-28 12:20:13.842 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:13.842 [Task submitter] INFO  nextflow.Session - [2b/0f3b66] Submitted process > HMMSEARCH (batch_14)
May-28 12:20:20.856 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/14/43ad94601f721538da8339b85ff869]
May-28 12:20:20.873 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:20.873 [Task submitter] INFO  nextflow.Session - [1f/36fdee] Submitted process > HMMSEARCH (batch_3)
May-28 12:20:21.124 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/0f3b66490ec78ee7b66c5a536084bf]
May-28 12:20:21.150 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:21.150 [Task submitter] INFO  nextflow.Session - [1c/6f7389] Submitted process > HMMSEARCH (batch_5)
May-28 12:20:28.443 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1f/36fdee5a803e0c2bd9508c90ed2c71]
May-28 12:20:28.467 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:28.468 [Task submitter] INFO  nextflow.Session - [01/fa7280] Submitted process > HMMSEARCH (batch_22)
May-28 12:20:28.620 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1c/6f738977a72105680129d969dbe22a]
May-28 12:20:28.655 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:28.655 [Task submitter] INFO  nextflow.Session - [c8/b39465] Submitted process > HMMSEARCH (batch_20)
May-28 12:20:35.621 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/01/fa728030110f006b73b5ef52f5a0c5]
May-28 12:20:35.635 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:35.636 [Task submitter] INFO  nextflow.Session - [6d/1cc3da] Submitted process > HMMSEARCH (batch_32)
May-28 12:20:36.106 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c8/b39465349c6eb0897a4851230bb094]
May-28 12:20:36.120 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:36.120 [Task submitter] INFO  nextflow.Session - [17/227e6b] Submitted process > HMMSEARCH (batch_25)
May-28 12:20:43.492 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6d/1cc3dad7dc92667ae3204771481bf8]
May-28 12:20:43.518 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:43.519 [Task submitter] INFO  nextflow.Session - [18/e6533d] Submitted process > HMMSEARCH (batch_11)
May-28 12:20:43.856 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/227e6bfd8eebaf0c9f7ccde07150ea]
May-28 12:20:43.874 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:43.875 [Task submitter] INFO  nextflow.Session - [ec/12a279] Submitted process > HMMSEARCH (batch_31)
May-28 12:20:51.508 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/18/e6533dc96b79feea3a9b36e526033e]
May-28 12:20:51.524 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:51.525 [Task submitter] INFO  nextflow.Session - [2f/b76018] Submitted process > HMMSEARCH (batch_27)
May-28 12:20:51.823 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ec/12a279d7129875b8a35f3bd49c15ad]
May-28 12:20:51.841 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:51.841 [Task submitter] INFO  nextflow.Session - [6f/5a865c] Submitted process > HMMSEARCH (batch_9)
May-28 12:20:59.056 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2f/b76018f81a4ce38fd67f037ac2f76d]
May-28 12:20:59.077 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:59.077 [Task submitter] INFO  nextflow.Session - [cb/70341e] Submitted process > HMMSEARCH (batch_30)
May-28 12:20:59.469 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6f/5a865c2eb82b165b0b992f47393156]
May-28 12:20:59.497 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:20:59.497 [Task submitter] INFO  nextflow.Session - [dc/9fa513] Submitted process > HMMSEARCH (batch_2)
May-28 12:21:08.775 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cb/70341e121f5b96c96d77adca358cb1]
May-28 12:21:08.799 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:08.800 [Task submitter] INFO  nextflow.Session - [40/211af5] Submitted process > HMMSEARCH (batch_1)
May-28 12:21:09.263 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dc/9fa513a494721b043b77fcc8ec03e2]
May-28 12:21:09.281 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:09.281 [Task submitter] INFO  nextflow.Session - [38/17881d] Submitted process > HMMSEARCH (batch_7)
May-28 12:21:17.173 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/211af51c3e698290fcca03309e9a26]
May-28 12:21:17.194 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:17.194 [Task submitter] INFO  nextflow.Session - [17/938a04] Submitted process > HMMSEARCH (batch_18)
May-28 12:21:17.780 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/38/17881de25894c922b3687a6744d167]
May-28 12:21:17.795 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:17.795 [Task submitter] INFO  nextflow.Session - [a5/db0cdd] Submitted process > HMMSEARCH (batch_15)
May-28 12:21:25.956 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/db0cdd7a2c999d193271d44e48d272]
May-28 12:21:25.967 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:25.967 [Task submitter] INFO  nextflow.Session - [24/15175d] Submitted process > HMMSEARCH (batch_24)
May-28 12:21:26.152 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/938a04d36dc978e167a351e607aa28]
May-28 12:21:26.179 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:26.180 [Task submitter] INFO  nextflow.Session - [e2/7310a0] Submitted process > HMMSEARCH (batch_16)
May-28 12:21:33.805 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/15175df3ee0aad7279719fcad57483]
May-28 12:21:33.818 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:33.818 [Task submitter] INFO  nextflow.Session - [24/3626ae] Submitted process > HMMSEARCH (batch_29)
May-28 12:21:33.911 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e2/7310a02a1c3dadb8d17a8c5a86bcef]
May-28 12:21:33.927 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:33.928 [Task submitter] INFO  nextflow.Session - [9d/9a8609] Submitted process > HMMSEARCH (batch_6)
May-28 12:21:41.267 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/3626ae11ab0327237c3791af16b6fb]
May-28 12:21:41.282 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:41.282 [Task submitter] INFO  nextflow.Session - [8f/f41c5a] Submitted process > HMMSEARCH (batch_17)
May-28 12:21:41.530 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9d/9a86091e81452606a791db14c4f0a8]
May-28 12:21:41.546 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:41.546 [Task submitter] INFO  nextflow.Session - [a7/f59f74] Submitted process > HMMSEARCH (batch_21)
May-28 12:21:48.832 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8f/f41c5ae3e71dbd3d597c4e0f45f156]
May-28 12:21:48.856 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:48.856 [Task submitter] INFO  nextflow.Session - [a3/05a910] Submitted process > HMMSEARCH (batch_19)
May-28 12:21:49.472 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a7/f59f74f79dcba3f36a07beb1ae4643]
May-28 12:21:49.485 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:49.486 [Task submitter] INFO  nextflow.Session - [68/2faee9] Submitted process > HMMSEARCH (batch_35)
May-28 12:21:57.242 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a3/05a9102185bab21a6680543c52dcf7]
May-28 12:21:57.257 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:57.261 [Task submitter] INFO  nextflow.Session - [2a/86af0a] Submitted process > HMMSEARCH (batch_34)
May-28 12:21:57.589 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/2faee95ace9b60b3f2d447712d1fa4]
May-28 12:21:57.602 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:21:57.603 [Task submitter] INFO  nextflow.Session - [5b/b7764d] Submitted process > HMMSEARCH (batch_28)
May-28 12:22:05.223 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/86af0ae999734e11f491e117fc881d]
May-28 12:22:05.240 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:05.240 [Task submitter] INFO  nextflow.Session - [90/6dbb6c] Submitted process > HMMSEARCH (batch_4)
May-28 12:22:05.430 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5b/b7764dab0c9894080673fc61712d0d]
May-28 12:22:05.446 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:05.447 [Task submitter] INFO  nextflow.Session - [89/28d762] Submitted process > HMMSEARCH (batch_26)
May-28 12:22:13.522 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/90/6dbb6cc8caae82f5bd4101551078de]
May-28 12:22:13.551 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:13.551 [Task submitter] INFO  nextflow.Session - [98/1863ae] Submitted process > HMMSEARCH (batch_36)
May-28 12:22:13.620 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/89/28d762383315a53d88b3b7f5a5cb96]
May-28 12:22:13.646 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:13.646 [Task submitter] INFO  nextflow.Session - [a0/e63884] Submitted process > HMMSEARCH (batch_33)
May-28 12:22:20.972 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/98/1863aebfb3a3feddfaca64414c7bae]
May-28 12:22:21.000 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:21.000 [Task submitter] INFO  nextflow.Session - [c9/8b6230] Submitted process > HMMSEARCH (batch_39)
May-28 12:22:21.334 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a0/e63884558a902ecf096b748adde27d]
May-28 12:22:21.349 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:21.349 [Task submitter] INFO  nextflow.Session - [cd/af5060] Submitted process > HMMSEARCH (batch_38)
May-28 12:22:28.546 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/8b6230c569c06f23a522ba26b3cd18]
May-28 12:22:28.559 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:28.560 [Task submitter] INFO  nextflow.Session - [ef/9fd2d0] Submitted process > HMMSEARCH (batch_40)
May-28 12:22:29.086 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cd/af5060322d1b1d61d9a38432f95680]
May-28 12:22:29.110 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:29.111 [Task submitter] INFO  nextflow.Session - [e3/89a4b4] Submitted process > HMMSEARCH (batch_37)
May-28 12:22:35.442 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/9fd2d092d1ae2b109fb4b7473ce5d8]
May-28 12:22:35.458 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:35.458 [Task submitter] INFO  nextflow.Session - [17/2aa1ba] Submitted process > HMMSEARCH (batch_44)
May-28 12:22:36.667 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e3/89a4b473d5a8e0f6132030c19142b4]
May-28 12:22:36.702 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:36.702 [Task submitter] INFO  nextflow.Session - [0e/cb462d] Submitted process > HMMSEARCH (batch_43)
May-28 12:22:42.729 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/2aa1bab3bb31876df2b680e3700f99]
May-28 12:22:42.743 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:42.744 [Task submitter] INFO  nextflow.Session - [ce/eb0bb9] Submitted process > HMMSEARCH (batch_42)
May-28 12:22:44.194 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0e/cb462d1bcdb21e5a028490a89393ac]
May-28 12:22:44.222 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:44.223 [Task submitter] INFO  nextflow.Session - [d7/fd3bfd] Submitted process > HMMSEARCH (batch_45)
May-28 12:22:49.587 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ce/eb0bb9ee80cbb738ffadb771add8ad]
May-28 12:22:49.601 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:49.601 [Task submitter] INFO  nextflow.Session - [cc/a9661a] Submitted process > HMMSEARCH (batch_41)
May-28 12:22:50.801 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d7/fd3bfd521f5cc52a544ec101b6350f]
May-28 12:22:50.813 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:50.814 [Task submitter] INFO  nextflow.Session - [09/74b194] Submitted process > HMMSEARCH (batch_50)
May-28 12:22:56.069 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cc/a9661a136612849fbb93711d548f22]
May-28 12:22:56.087 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:56.088 [Task submitter] INFO  nextflow.Session - [15/acc2d6] Submitted process > HMMSEARCH (batch_49)
May-28 12:22:57.657 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/09/74b194926d4518d57c001ef7f917bc]
May-28 12:22:57.675 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:22:57.676 [Task submitter] INFO  nextflow.Session - [b4/866584] Submitted process > HMMSEARCH (batch_48)
May-28 12:23:02.744 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/acc2d6cee80fdf278b50498f02e963]
May-28 12:23:02.758 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:02.759 [Task submitter] INFO  nextflow.Session - [ca/5f8af9] Submitted process > HMMSEARCH (batch_53)
May-28 12:23:04.357 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b4/866584c0485e7a50c5fbdd7e67d1d7]
May-28 12:23:04.370 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:04.370 [Task submitter] INFO  nextflow.Session - [d4/623cd8] Submitted process > HMMSEARCH (batch_52)
May-28 12:23:09.336 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ca/5f8af941b31523987280d52ac5c759]
May-28 12:23:09.349 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:09.349 [Task submitter] INFO  nextflow.Session - [ba/de36ef] Submitted process > HMMSEARCH (batch_51)
May-28 12:23:11.714 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d4/623cd8552e39354c59159772ac1671]
May-28 12:23:11.736 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:11.737 [Task submitter] INFO  nextflow.Session - [5e/48de8a] Submitted process > HMMSEARCH (batch_47)
May-28 12:23:17.322 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/de36ef880a47696b1741135209d840]
May-28 12:23:17.361 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:17.361 [Task submitter] INFO  nextflow.Session - [4a/fa0488] Submitted process > HMMSEARCH (batch_54)
May-28 12:23:20.193 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5e/48de8aaab7fb2d1e6734fc0cfbc833]
May-28 12:23:20.207 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:20.207 [Task submitter] INFO  nextflow.Session - [f7/0a3d50] Submitted process > HMMSEARCH (batch_56)
May-28 12:23:24.830 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4a/fa0488bfd43fb7d565a84dab48be99]
May-28 12:23:24.859 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:24.859 [Task submitter] INFO  nextflow.Session - [47/46b7e3] Submitted process > HMMSEARCH (batch_59)
May-28 12:23:27.893 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f7/0a3d5043565165c79476d967717ac1]
May-28 12:23:27.921 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:27.921 [Task submitter] INFO  nextflow.Session - [2e/5a1d88] Submitted process > HMMSEARCH (batch_60)
May-28 12:23:32.616 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/46b7e354964e7e4e5d76d235c7eadc]
May-28 12:23:32.627 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:32.628 [Task submitter] INFO  nextflow.Session - [f1/661648] Submitted process > HMMSEARCH (batch_61)
May-28 12:23:35.409 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2e/5a1d88fd86a05d97fdee30d841ca70]
May-28 12:23:35.435 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:35.435 [Task submitter] INFO  nextflow.Session - [ea/a609bb] Submitted process > HMMSEARCH (batch_58)
May-28 12:23:40.514 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/66164804336153f67f2c1303c84331]
May-28 12:23:40.527 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:40.528 [Task submitter] INFO  nextflow.Session - [d4/a1ce3e] Submitted process > HMMSEARCH (batch_55)
May-28 12:23:43.291 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ea/a609bb790c2040be4abbd6bc154e20]
May-28 12:23:43.307 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:43.308 [Task submitter] INFO  nextflow.Session - [6f/736fea] Submitted process > HMMSEARCH (batch_62)
May-28 12:23:48.359 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d4/a1ce3e5a44175553c108ebb564c7c2]
May-28 12:23:48.373 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:48.373 [Task submitter] INFO  nextflow.Session - [40/3f358a] Submitted process > HMMSEARCH (batch_66)
May-28 12:23:49.388 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/3f358ac1ee3bd838b02987f2a1c6d5]
May-28 12:23:49.403 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:49.403 [Task submitter] INFO  nextflow.Session - [16/9bb47b] Submitted process > HMMSEARCH (batch_63)
May-28 12:23:51.014 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6f/736feaa04f7938de10a8740fdcc368]
May-28 12:23:51.031 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:51.031 [Task submitter] INFO  nextflow.Session - [16/4f3333] Submitted process > HMMSEARCH (batch_64)
May-28 12:23:56.298 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/9bb47bdb2b8516f8a22e13bc2ba711]
May-28 12:23:56.315 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:56.315 [Task submitter] INFO  nextflow.Session - [63/515511] Submitted process > HMMSEARCH (batch_65)
May-28 12:23:58.400 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/4f3333fdac7f30b64a6517ead84f4c]
May-28 12:23:58.428 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:23:58.428 [Task submitter] INFO  nextflow.Session - [a0/4c3ac5] Submitted process > HMMSEARCH (batch_57)
May-28 12:24:04.172 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/63/51551175a2e4f86b383b879f8718c3]
May-28 12:24:04.186 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:04.186 [Task submitter] INFO  nextflow.Session - [71/d97e19] Submitted process > HMMSEARCH (batch_46)
May-28 12:24:06.272 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a0/4c3ac5d00fae34e9ba9f7adaf3e398]
May-28 12:24:06.288 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.289 [Task submitter] INFO  nextflow.Session - [43/827606] Submitted process > PROCESS_HITS (batch_23)
May-28 12:24:06.309 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.309 [Task submitter] INFO  nextflow.Session - [be/b90710] Submitted process > PROCESS_HITS (batch_10)
May-28 12:24:06.329 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.329 [Task submitter] INFO  nextflow.Session - [f3/d68001] Submitted process > PROCESS_HITS (batch_8)
May-28 12:24:06.346 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.346 [Task submitter] INFO  nextflow.Session - [75/5fd4d5] Submitted process > PROCESS_HITS (batch_12)
May-28 12:24:06.359 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.359 [Task submitter] INFO  nextflow.Session - [f0/848cd7] Submitted process > PROCESS_HITS (batch_13)
May-28 12:24:06.381 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.381 [Task submitter] INFO  nextflow.Session - [be/9816c6] Submitted process > PROCESS_HITS (batch_14)
May-28 12:24:06.399 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.400 [Task submitter] INFO  nextflow.Session - [b4/f23ee9] Submitted process > PROCESS_HITS (batch_3)
May-28 12:24:06.416 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:06.416 [Task submitter] INFO  nextflow.Session - [8e/b172a5] Submitted process > PROCESS_HITS (batch_5)
May-28 12:24:12.978 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/d97e19f9452565d80749e36137f6ff]
May-28 12:24:13.005 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.006 [Task submitter] INFO  nextflow.Session - [e8/a9d93f] Submitted process > PROCESS_HITS (batch_22)
May-28 12:24:13.025 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.026 [Task submitter] INFO  nextflow.Session - [9c/1826af] Submitted process > PROCESS_HITS (batch_20)
May-28 12:24:13.045 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.045 [Task submitter] INFO  nextflow.Session - [35/60726b] Submitted process > PROCESS_HITS (batch_32)
May-28 12:24:13.061 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.062 [Task submitter] INFO  nextflow.Session - [67/448b84] Submitted process > PROCESS_HITS (batch_25)
May-28 12:24:13.092 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.092 [Task submitter] INFO  nextflow.Session - [12/879627] Submitted process > PROCESS_HITS (batch_11)
May-28 12:24:13.129 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.129 [Task submitter] INFO  nextflow.Session - [b1/6f5dd1] Submitted process > PROCESS_HITS (batch_31)
May-28 12:24:13.155 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.155 [Task submitter] INFO  nextflow.Session - [04/4fb5d4] Submitted process > PROCESS_HITS (batch_27)
May-28 12:24:13.173 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:13.173 [Task submitter] INFO  nextflow.Session - [3b/803ec8] Submitted process > PROCESS_HITS (batch_9)
May-28 12:24:25.646 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b4/f23ee93048335f7a23400317632636]
May-28 12:24:25.671 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:25.672 [Task submitter] INFO  nextflow.Session - [3a/adbaa5] Submitted process > PROCESS_HITS (batch_30)
May-28 12:24:25.844 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/b172a564f54a7d3352e7e0d9613dd4]
May-28 12:24:25.869 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:25.870 [Task submitter] INFO  nextflow.Session - [22/d555af] Submitted process > PROCESS_HITS (batch_2)
May-28 12:24:26.225 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f0/848cd7ae096ae1e1fb5139e3932643]
May-28 12:24:26.272 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:26.272 [Task submitter] INFO  nextflow.Session - [5a/61b7c3] Submitted process > PROCESS_HITS (batch_1)
May-28 12:24:26.323 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/be/9816c6fdebd0d8391f1336baec41b9]
May-28 12:24:26.340 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:26.340 [Task submitter] INFO  nextflow.Session - [e7/010ea5] Submitted process > PROCESS_HITS (batch_7)
May-28 12:24:26.556 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/75/5fd4d5b4ed1b7dcc44250cc96a70bb]
May-28 12:24:26.574 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:26.575 [Task submitter] INFO  nextflow.Session - [b1/4df2fe] Submitted process > PROCESS_HITS (batch_15)
May-28 12:24:26.787 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f3/d68001d0c9483e5a4044c1376bb879]
May-28 12:24:26.811 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:26.811 [Task submitter] INFO  nextflow.Session - [0c/7e5bfd] Submitted process > PROCESS_HITS (batch_18)
May-28 12:24:26.925 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/be/b90710a62419290ca5cd9cba79ec01]
May-28 12:24:26.963 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:26.964 [Task submitter] INFO  nextflow.Session - [22/b5176f] Submitted process > PROCESS_HITS (batch_24)
May-28 12:24:26.967 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/43/82760601b6b807d56367fb3a2b2eb3]
May-28 12:24:26.992 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:26.992 [Task submitter] INFO  nextflow.Session - [d1/6965ba] Submitted process > PROCESS_HITS (batch_16)
May-28 12:24:32.627 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3b/803ec8bb5aec0bc945df00b3512bfa]
May-28 12:24:32.651 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:32.651 [Task submitter] INFO  nextflow.Session - [6b/a7eade] Submitted process > PROCESS_HITS (batch_29)
May-28 12:24:33.081 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/04/4fb5d4324102a4e422abf15920ff79]
May-28 12:24:33.097 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:33.097 [Task submitter] INFO  nextflow.Session - [1a/362012] Submitted process > PROCESS_HITS (batch_6)
May-28 12:24:33.157 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/35/60726b7ca34622ff1983feb6caa2d0]
May-28 12:24:33.176 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:33.176 [Task submitter] INFO  nextflow.Session - [c3/19cfa8] Submitted process > PROCESS_HITS (batch_17)
May-28 12:24:33.194 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/67/448b84bc2416d3981b06db994de7f7]
May-28 12:24:33.230 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:33.230 [Task submitter] INFO  nextflow.Session - [47/f2c394] Submitted process > PROCESS_HITS (batch_21)
May-28 12:24:33.656 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9c/1826af9b4ba7963d0ef3639e562d97]
May-28 12:24:33.687 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:33.687 [Task submitter] INFO  nextflow.Session - [f5/8695f3] Submitted process > PROCESS_HITS (batch_19)
May-28 12:24:33.825 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e8/a9d93f3ad0694f23ca57655810ce6b]
May-28 12:24:33.865 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:33.866 [Task submitter] INFO  nextflow.Session - [98/d1dfad] Submitted process > PROCESS_HITS (batch_35)
May-28 12:24:33.969 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/6f5dd112d9faa994ef8c7248c3f66d]
May-28 12:24:33.996 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:33.996 [Task submitter] INFO  nextflow.Session - [e8/b119ea] Submitted process > PROCESS_HITS (batch_34)
May-28 12:24:34.029 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/8796272c2f01c8bba6ee73df754a39]
May-28 12:24:34.042 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:34.042 [Task submitter] INFO  nextflow.Session - [13/e75466] Submitted process > PROCESS_HITS (batch_28)
May-28 12:24:42.195 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/22/d555afa09989eb230ca01e1d841e49]
May-28 12:24:42.210 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:42.211 [Task submitter] INFO  nextflow.Session - [35/14f38e] Submitted process > PROCESS_HITS (batch_4)
May-28 12:24:42.786 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3a/adbaa52eb7c027965cb5f8f11086e6]
May-28 12:24:42.815 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:42.816 [Task submitter] INFO  nextflow.Session - [83/3b7ba7] Submitted process > PROCESS_HITS (batch_26)
May-28 12:24:42.852 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/61b7c387fc08f6bcf16259171910ed]
May-28 12:24:42.876 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:42.876 [Task submitter] INFO  nextflow.Session - [ef/20ed11] Submitted process > PROCESS_HITS (batch_36)
May-28 12:24:43.263 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e7/010ea542d23f333f5f96d09df0d694]
May-28 12:24:43.285 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:43.285 [Task submitter] INFO  nextflow.Session - [15/e9febe] Submitted process > PROCESS_HITS (batch_33)
May-28 12:24:43.452 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/4df2feb8373abf8255a36e5deaf541]
May-28 12:24:43.463 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:43.463 [Task submitter] INFO  nextflow.Session - [1b/8f8268] Submitted process > PROCESS_HITS (batch_39)
May-28 12:24:43.667 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0c/7e5bfd423bbc31fc2c6a7bac09e294]
May-28 12:24:43.685 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:43.685 [Task submitter] INFO  nextflow.Session - [64/b07d6e] Submitted process > PROCESS_HITS (batch_38)
May-28 12:24:43.776 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d1/6965ba87b804855c9a339bdbe5b1f5]
May-28 12:24:43.793 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:43.793 [Task submitter] INFO  nextflow.Session - [f5/97f7da] Submitted process > PROCESS_HITS (batch_40)
May-28 12:24:43.820 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/22/b5176f832294c5d874aba87eb2fdfd]
May-28 12:24:43.848 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:43.848 [Task submitter] INFO  nextflow.Session - [4f/43d999] Submitted process > PROCESS_HITS (batch_37)
May-28 12:24:47.531 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/a7eade65ad713a34d82907cd9bd2de]
May-28 12:24:47.558 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:47.559 [Task submitter] INFO  nextflow.Session - [aa/79ba0b] Submitted process > PROCESS_HITS (batch_44)
May-28 12:24:48.442 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1a/362012e8d10d059e3db096b5c03f5b]
May-28 12:24:48.455 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:48.464 [Task submitter] INFO  nextflow.Session - [b2/53f627] Submitted process > PROCESS_HITS (batch_43)
May-28 12:24:49.163 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/98/d1dfad9ee88854c0c14f16e686b8a8]
May-28 12:24:49.188 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:49.189 [Task submitter] INFO  nextflow.Session - [1d/c9a486] Submitted process > PROCESS_HITS (batch_42)
May-28 12:24:49.219 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c3/19cfa8809fd23d25994672e9cc94f2]
May-28 12:24:49.234 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:49.234 [Task submitter] INFO  nextflow.Session - [4a/534d96] Submitted process > PROCESS_HITS (batch_45)
May-28 12:24:49.253 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/e75466cd6aff454fe0d30cfaeb6236]
May-28 12:24:49.266 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:49.266 [Task submitter] INFO  nextflow.Session - [28/1e7df5] Submitted process > PROCESS_HITS (batch_41)
May-28 12:24:49.270 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/f2c394ddfb6bdc43f1861713bd8b18]
May-28 12:24:49.300 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:49.300 [Task submitter] INFO  nextflow.Session - [f6/791168] Submitted process > PROCESS_HITS (batch_50)
May-28 12:24:49.586 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e8/b119eac68eac0312cc89229b100334]
May-28 12:24:49.608 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:49.609 [Task submitter] INFO  nextflow.Session - [90/5087ea] Submitted process > PROCESS_HITS (batch_49)
May-28 12:24:49.878 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/8695f35d6c62778366337d952629d5]
May-28 12:24:49.907 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:49.908 [Task submitter] INFO  nextflow.Session - [28/f20afd] Submitted process > PROCESS_HITS (batch_48)
May-28 12:24:52.482 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 16 -- submitted tasks are shown below
~> TaskHandler[id: 100; name: PROCESS_HITS (batch_4); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/35/14f38e4c6f0b6c4b2de83f3ff98cba]
~> TaskHandler[id: 101; name: PROCESS_HITS (batch_26); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/83/3b7ba702ec3f0d7ea3b2a7e525d04d]
~> TaskHandler[id: 102; name: PROCESS_HITS (batch_36); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/20ed11fa473542793f3e1b7253cd18]
~> TaskHandler[id: 103; name: PROCESS_HITS (batch_33); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/e9febeae06fab72dcbbf7c87b23595]
~> TaskHandler[id: 104; name: PROCESS_HITS (batch_39); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1b/8f8268143d5b8b1356b053dad3a3e3]
~> TaskHandler[id: 105; name: PROCESS_HITS (batch_38); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/64/b07d6ed61415b7957d828ff4aae695]
~> TaskHandler[id: 106; name: PROCESS_HITS (batch_40); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/97f7dab6630f30d164d859d28e8d0d]
~> TaskHandler[id: 107; name: PROCESS_HITS (batch_37); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4f/43d99941d041d9beac4984f5211949]
~> TaskHandler[id: 108; name: PROCESS_HITS (batch_44); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/aa/79ba0b5e1b955ec13d29669a3d9948]
~> TaskHandler[id: 109; name: PROCESS_HITS (batch_43); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/53f6276874f73460880c53861e3e8d]
.. remaining tasks omitted.
May-28 12:24:57.795 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/20ed11fa473542793f3e1b7253cd18]
May-28 12:24:57.810 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:57.810 [Task submitter] INFO  nextflow.Session - [be/68f6bc] Submitted process > PROCESS_HITS (batch_53)
May-28 12:24:58.029 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/35/14f38e4c6f0b6c4b2de83f3ff98cba]
May-28 12:24:58.045 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:58.046 [Task submitter] INFO  nextflow.Session - [e4/261e20] Submitted process > PROCESS_HITS (batch_52)
May-28 12:24:58.490 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/83/3b7ba702ec3f0d7ea3b2a7e525d04d]
May-28 12:24:58.524 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:58.524 [Task submitter] INFO  nextflow.Session - [f1/859bad] Submitted process > PROCESS_HITS (batch_51)
May-28 12:24:58.681 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/e9febeae06fab72dcbbf7c87b23595]
May-28 12:24:58.694 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:58.694 [Task submitter] INFO  nextflow.Session - [af/28c334] Submitted process > PROCESS_HITS (batch_47)
May-28 12:24:59.449 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/64/b07d6ed61415b7957d828ff4aae695]
May-28 12:24:59.467 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:59.467 [Task submitter] INFO  nextflow.Session - [f7/af54a1] Submitted process > PROCESS_HITS (batch_54)
May-28 12:24:59.538 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1b/8f8268143d5b8b1356b053dad3a3e3]
May-28 12:24:59.562 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:59.563 [Task submitter] INFO  nextflow.Session - [82/ff423f] Submitted process > PROCESS_HITS (batch_56)
May-28 12:24:59.682 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4f/43d99941d041d9beac4984f5211949]
May-28 12:24:59.696 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:24:59.696 [Task submitter] INFO  nextflow.Session - [e5/08c000] Submitted process > PROCESS_HITS (batch_59)
May-28 12:25:00.140 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/97f7dab6630f30d164d859d28e8d0d]
May-28 12:25:00.167 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:00.168 [Task submitter] INFO  nextflow.Session - [f1/811bda] Submitted process > PROCESS_HITS (batch_60)
May-28 12:25:00.169 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 10 -- tasks to be submitted are shown below
~> TaskHandler[id: 124; name: PROCESS_HITS (batch_61); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/02/d7dc5630b2737186cc600b4227fd40]
~> TaskHandler[id: 125; name: PROCESS_HITS (batch_58); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/44/197faa303dfbea05487fe2eb3ab28a]
~> TaskHandler[id: 126; name: PROCESS_HITS (batch_55); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e9/bde3fa21a5018e0e60474be728750d]
~> TaskHandler[id: 127; name: PROCESS_HITS (batch_66); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/d1d6b2ad2e6529cc0d1be55dc4f03e]
~> TaskHandler[id: 128; name: PROCESS_HITS (batch_62); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4b/3cb2a041e2fab7a777a6b337f80b6b]
~> TaskHandler[id: 129; name: PROCESS_HITS (batch_63); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/35/e06c698ce03e2fccc99b016c7bb869]
~> TaskHandler[id: 130; name: PROCESS_HITS (batch_64); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5c/b54699b348d9cbb51842339e07fe00]
~> TaskHandler[id: 131; name: PROCESS_HITS (batch_65); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1f/b9ac228db14c486c4bda7a05d17d32]
~> TaskHandler[id: 132; name: PROCESS_HITS (batch_57); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7c/dafe97423bdd27d9e146121912a7ff]
~> TaskHandler[id: 133; name: PROCESS_HITS (batch_46); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fc/d9a5f469e6b1fc1f6baf4edf922a7c]
May-28 12:25:02.292 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/aa/79ba0b5e1b955ec13d29669a3d9948]
May-28 12:25:02.323 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:02.323 [Task submitter] INFO  nextflow.Session - [02/d7dc56] Submitted process > PROCESS_HITS (batch_61)
May-28 12:25:03.555 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/53f6276874f73460880c53861e3e8d]
May-28 12:25:03.582 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:03.582 [Task submitter] INFO  nextflow.Session - [44/197faa] Submitted process > PROCESS_HITS (batch_58)
May-28 12:25:03.623 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1d/c9a486294b91c47ac91edb9eabaf0f]
May-28 12:25:03.639 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:03.639 [Task submitter] INFO  nextflow.Session - [e9/bde3fa] Submitted process > PROCESS_HITS (batch_55)
May-28 12:25:03.764 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4a/534d965cb5699fdb53c2f44ce02777]
May-28 12:25:03.779 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:03.779 [Task submitter] INFO  nextflow.Session - [c4/d1d6b2] Submitted process > PROCESS_HITS (batch_66)
May-28 12:25:03.810 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/28/1e7df5b474a0d07bd5523a5364429b]
May-28 12:25:03.845 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:03.845 [Task submitter] INFO  nextflow.Session - [4b/3cb2a0] Submitted process > PROCESS_HITS (batch_62)
May-28 12:25:04.244 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/90/5087ea4c3748d75622dd6e4c1779db]
May-28 12:25:04.270 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:04.270 [Task submitter] INFO  nextflow.Session - [35/e06c69] Submitted process > PROCESS_HITS (batch_63)
May-28 12:25:04.515 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f6/791168e75eab50f4c17d13778eace9]
May-28 12:25:04.528 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/28/f20afddc6d4d357df3119906910623]
May-28 12:25:04.531 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:04.531 [Task submitter] INFO  nextflow.Session - [5c/b54699] Submitted process > PROCESS_HITS (batch_64)
May-28 12:25:04.556 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:04.556 [Task submitter] INFO  nextflow.Session - [1f/b9ac22] Submitted process > PROCESS_HITS (batch_65)
May-28 12:25:05.915 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/d1d6b2ad2e6529cc0d1be55dc4f03e]
May-28 12:25:05.936 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:05.936 [Task submitter] INFO  nextflow.Session - [7c/dafe97] Submitted process > PROCESS_HITS (batch_57)
May-28 12:25:12.017 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/be/68f6bc00737ea6134775fa09c9211d]
May-28 12:25:12.038 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:25:12.039 [Task submitter] INFO  nextflow.Session - [fc/d9a5f4] Submitted process > PROCESS_HITS (batch_46)
May-28 12:25:12.385 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e4/261e20a66d20d01cf25da7f024a668]
May-28 12:25:13.389 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/859badaa8052cd445b75b6d7993449]
May-28 12:25:14.173 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/28c334140fa4e086945cca0a0a9943]
May-28 12:25:15.079 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f7/af54a1f1c1863ec47133ab3bb58283]
May-28 12:25:15.396 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/811bdab033a24d018c11c3a277cb74]
May-28 12:25:15.403 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/82/ff423f0e8ddafff9e577039b0113c0]
May-28 12:25:15.809 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/08c000767e38e98d81a1c7e76e5d0b]
May-28 12:25:18.319 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/02/d7dc5630b2737186cc600b4227fd40]
May-28 12:25:19.286 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e9/bde3fa21a5018e0e60474be728750d]
May-28 12:25:19.812 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4b/3cb2a041e2fab7a777a6b337f80b6b]
May-28 12:25:20.140 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/35/e06c698ce03e2fccc99b016c7bb869]
May-28 12:25:20.381 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/44/197faa303dfbea05487fe2eb3ab28a]
May-28 12:25:20.706 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1f/b9ac228db14c486c4bda7a05d17d32]
May-28 12:25:20.854 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5c/b54699b348d9cbb51842339e07fe00]
May-28 12:25:22.188 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7c/dafe97423bdd27d9e146121912a7ff]
May-28 12:25:28.392 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fc/d9a5f469e6b1fc1f6baf4edf922a7c]
May-28 12:27:33.953 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 12:27:33.954 [Task submitter] INFO  nextflow.Session - [df/96970f] Submitted process > CREATE_DATAFRAME (1)
May-28 12:29:52.526 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 12:34:52.542 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 12:39:52.568 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 12:44:52.591 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 12:49:52.634 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 12:54:52.675 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 12:59:52.721 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 13:01:42.104 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/96970fc520146bfde4d9b16ed43cf9]
May-28 13:01:42.140 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 13:01:42.140 [Task submitter] INFO  nextflow.Session - [92/2dcf22] Submitted process > AUTOENCODER (1)
May-28 13:02:51.915 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: AUTOENCODER (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/92/2dcf226c4a2b3a117dd26dabb188aa]
May-28 13:02:51.970 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 13:02:51.970 [Task submitter] INFO  nextflow.Session - [0a/76065c] Submitted process > MODEL_PREDICT (1)
May-28 13:02:54.093 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 136; name: MODEL_PREDICT (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/76065c932a3fb9876ede534605cdd1]
May-28 13:02:54.097 [main] DEBUG nextflow.Session - Session await > all processes finished
May-28 13:02:54.193 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-28 13:02:54.193 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-28 13:02:54.197 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
May-28 13:02:54.197 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
May-28 13:02:54.202 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=136; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=5h 5m 37s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=18; peakCpus=36; peakMemory=72 GB; ]
May-28 13:02:54.325 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-28 13:02:54.356 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
May-28 13:02:54.356 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
