Jun-02 14:57:29.079 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
Jun-02 14:57:29.199 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
Jun-02 14:57:29.219 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
Jun-02 14:57:29.241 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-02 14:57:29.242 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-02 14:57:29.245 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-02 14:57:29.257 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-02 14:57:29.274 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-02 14:57:29.277 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-02 14:57:29.301 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
Jun-02 14:57:29.303 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@9f6e406] - activable => nextflow.secret.LocalSecretsProvider@9f6e406
Jun-02 14:57:29.328 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Jun-02 14:57:29.814 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-02 14:57:29.829 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [tender_dijkstra] DSL2 - revision: e9019381d3
Jun-02 14:57:29.830 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-02 14:57:29.831 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-02 14:57:29.876 [main] DEBUG nextflow.Session - Session UUID: 2305699c-95e7-4455-aa0e-bd5c126ca57c
Jun-02 14:57:29.877 [main] DEBUG nextflow.Session - Run name: tender_dijkstra
Jun-02 14:57:29.878 [main] DEBUG nextflow.Session - Executor pool size: 32
Jun-02 14:57:29.888 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-02 14:57:29.892 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-02 14:57:29.914 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (77.4 GB) - Swap: 8 GB (0)
Jun-02 14:57:29.949 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
Jun-02 14:57:29.982 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-02 14:57:29.991 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-02 14:57:29.995 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-02 14:57:30.022 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-02 14:57:30.030 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
Jun-02 14:57:30.150 [main] DEBUG nextflow.Session - Session start
Jun-02 14:57:30.796 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-02 14:57:30.836 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

Jun-02 14:57:30.940 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
Jun-02 14:57:30.950 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:57:30.951 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:57:30.956 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-02 14:57:30.962 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
Jun-02 14:57:30.963 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-02 14:57:30.983 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
Jun-02 14:57:31.035 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
Jun-02 14:57:31.052 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:57:31.052 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:57:31.053 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
Jun-02 14:57:31.055 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
Jun-02 14:57:31.068 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
Jun-02 14:57:31.071 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:57:31.071 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:57:31.073 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
Jun-02 14:57:31.074 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
Jun-02 14:57:31.078 [main] INFO  nextflow.Nextflow - Found existing final_results.csv, skipping CREATE_DATAFRAME process
Jun-02 14:57:31.085 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:57:31.085 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:57:31.086 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
Jun-02 14:57:31.092 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-02 14:57:31.092 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-02 14:57:31.093 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
Jun-02 14:57:31.099 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
Jun-02 14:57:31.102 [main] DEBUG nextflow.Session - Igniting dataflow network (21)
Jun-02 14:57:31.117 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
Jun-02 14:57:31.118 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-02 14:57:31.119 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-02 14:57:31.120 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-02 14:57:31.121 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-02 14:57:31.123 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
Jun-02 14:57:31.124 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
Jun-02 14:57:31.127 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_1343ce0d68727479: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Jun-02 14:57:31.128 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-02 14:57:31.129 [main] DEBUG nextflow.Session - Session await
Jun-02 14:57:31.252 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:57:31.254 [Task submitter] INFO  nextflow.Session - [79/906993] Submitted process > AUTOENCODER (1)
Jun-02 14:57:31.282 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-02 14:57:31.283 [Task submitter] INFO  nextflow.Session - [bd/05212d] Submitted process > PRODIGAL (1)
