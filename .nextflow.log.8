May-28 11:47:31.705 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
May-28 11:47:31.791 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-28 11:47:31.812 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-28 11:47:31.834 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-28 11:47:31.837 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-28 11:47:31.840 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-28 11:47:31.851 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-28 11:47:31.868 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 11:47:31.870 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 11:47:31.907 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-28 11:47:31.911 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7e97551f] - activable => nextflow.secret.LocalSecretsProvider@7e97551f
May-28 11:47:31.922 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-28 11:47:32.400 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-28 11:47:32.415 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [tiny_rutherford] DSL2 - revision: e40fbdbdc4
May-28 11:47:32.416 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-28 11:47:32.416 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-28 11:47:32.459 [main] DEBUG nextflow.Session - Session UUID: 4908f32d-9a3c-44bc-9738-40d4467dd68c
May-28 11:47:32.460 [main] DEBUG nextflow.Session - Run name: tiny_rutherford
May-28 11:47:32.463 [main] DEBUG nextflow.Session - Executor pool size: 32
May-28 11:47:32.471 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-28 11:47:32.475 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 11:47:32.493 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (7.8 GB) - Swap: 8 GB (48 KB)
May-28 11:47:32.534 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-28 11:47:32.564 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-28 11:47:32.574 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-28 11:47:32.579 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-28 11:47:32.604 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-28 11:47:32.613 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-28 11:47:32.747 [main] DEBUG nextflow.Session - Session start
May-28 11:47:33.417 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-28 11:47:33.453 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-28 11:47:33.559 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-28 11:47:33.569 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:47:33.569 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:47:33.577 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-28 11:47:33.582 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-28 11:47:33.584 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-28 11:47:33.608 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-28 11:47:33.660 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-28 11:47:33.677 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:47:33.677 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:47:33.680 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-28 11:47:33.686 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-28 11:47:33.698 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-28 11:47:33.700 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:47:33.708 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:47:33.717 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-28 11:47:33.718 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-28 11:47:33.739 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-28 11:47:33.741 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:47:33.741 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:47:33.743 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-28 11:47:33.760 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:47:33.760 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:47:33.761 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-28 11:47:33.766 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 11:47:33.767 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 11:47:33.769 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-28 11:47:33.773 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-28 11:47:33.775 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
May-28 11:47:33.782 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-28 11:47:33.782 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 11:47:33.783 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 11:47:33.785 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 11:47:33.786 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 11:47:33.787 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-28 11:47:33.797 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-28 11:47:33.799 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-28 11:47:33.800 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_d2d17cc0bf6f9b00: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-28 11:47:33.801 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-28 11:47:33.802 [main] DEBUG nextflow.Session - Session await
May-28 11:47:33.915 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:47:33.917 [Task submitter] INFO  nextflow.Session - [d7/106b29] Submitted process > PRODIGAL (1)
May-28 11:47:41.122 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d7/106b299d603caca7ffef0aeffeb2bf]
May-28 11:47:41.124 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 11:47:41.144 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 11:47:41.205 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:47:41.205 [Task submitter] INFO  nextflow.Session - [af/29910c] Submitted process > HMMSEARCH (batch_17)
May-28 11:47:41.227 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:47:41.227 [Task submitter] INFO  nextflow.Session - [52/1e4c7b] Submitted process > HMMSEARCH (batch_16)
May-28 11:47:47.324 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/1e4c7bfae6abb0addadc44a5aa3aec]
May-28 11:47:47.337 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:47:47.338 [Task submitter] INFO  nextflow.Session - [3a/81ed94] Submitted process > HMMSEARCH (batch_5)
May-28 11:47:47.486 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/29910c3919f81a920fcf2b9e06db30]
May-28 11:47:47.498 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:47:47.498 [Task submitter] INFO  nextflow.Session - [e2/d73607] Submitted process > HMMSEARCH (batch_3)
May-28 11:47:54.050 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3a/81ed94222af55a7727f68aca20636a]
May-28 11:47:54.064 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:47:54.064 [Task submitter] INFO  nextflow.Session - [e2/a34b9c] Submitted process > HMMSEARCH (batch_7)
May-28 11:47:54.250 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e2/d73607db29b1dc4de12faf149690db]
May-28 11:47:54.264 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:47:54.264 [Task submitter] INFO  nextflow.Session - [2a/c7fa25] Submitted process > HMMSEARCH (batch_25)
May-28 11:48:00.750 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e2/a34b9caae9e311d643c55d5d099882]
May-28 11:48:00.751 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/c7fa259492454e763a4fbe68f30157]
May-28 11:48:00.768 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:00.769 [Task submitter] INFO  nextflow.Session - [e9/40cb3f] Submitted process > HMMSEARCH (batch_8)
May-28 11:48:00.786 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:00.794 [Task submitter] INFO  nextflow.Session - [66/5953b0] Submitted process > HMMSEARCH (batch_2)
May-28 11:48:06.935 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/66/5953b0dbf94ad7601bb94208e6e031]
May-28 11:48:06.944 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e9/40cb3fc4ace40d88dc157176ac6fcb]
May-28 11:48:06.945 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:06.945 [Task submitter] INFO  nextflow.Session - [52/c38c42] Submitted process > HMMSEARCH (batch_34)
May-28 11:48:06.980 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:06.980 [Task submitter] INFO  nextflow.Session - [ba/e7e533] Submitted process > HMMSEARCH (batch_19)
May-28 11:48:14.285 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/c38c42a00352824d7469a25eb02be5]
May-28 11:48:14.312 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:14.312 [Task submitter] INFO  nextflow.Session - [4d/822c24] Submitted process > HMMSEARCH (batch_26)
May-28 11:48:14.700 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/e7e53377da993bc9f0f3dfd95229e2]
May-28 11:48:14.713 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:14.713 [Task submitter] INFO  nextflow.Session - [85/811872] Submitted process > HMMSEARCH (batch_31)
May-28 11:48:20.629 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4d/822c2479074efeacdcd47d89b0939e]
May-28 11:48:20.646 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:20.646 [Task submitter] INFO  nextflow.Session - [bb/c8cce2] Submitted process > HMMSEARCH (batch_23)
May-28 11:48:20.751 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/85/8118729543adc4cc0923b683971beb]
May-28 11:48:20.763 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:20.763 [Task submitter] INFO  nextflow.Session - [5a/a0a178] Submitted process > HMMSEARCH (batch_32)
May-28 11:48:26.863 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bb/c8cce2956183953dcc272476b95337]
May-28 11:48:26.885 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:26.885 [Task submitter] INFO  nextflow.Session - [a6/f063f2] Submitted process > HMMSEARCH (batch_10)
May-28 11:48:27.081 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/a0a178d70dd8cfc49cbc2c69286a65]
May-28 11:48:27.093 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:27.093 [Task submitter] INFO  nextflow.Session - [28/5f7b4f] Submitted process > HMMSEARCH (batch_6)
May-28 11:48:33.518 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a6/f063f2d4f94b2e5efbab0a90bc5cee]
May-28 11:48:33.533 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:33.533 [Task submitter] INFO  nextflow.Session - [b1/cccb91] Submitted process > HMMSEARCH (batch_20)
May-28 11:48:33.557 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/28/5f7b4ffc6a8dbaed3d9ace7a81068d]
May-28 11:48:33.569 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:33.569 [Task submitter] INFO  nextflow.Session - [7f/942024] Submitted process > HMMSEARCH (batch_18)
May-28 11:48:40.091 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/cccb914f607e7c7226c9ae1d26e286]
May-28 11:48:40.098 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/9420242fda2fcea73f0c1967a59382]
May-28 11:48:40.103 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:40.103 [Task submitter] INFO  nextflow.Session - [f3/5139ba] Submitted process > HMMSEARCH (batch_21)
May-28 11:48:40.125 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:40.125 [Task submitter] INFO  nextflow.Session - [fd/43a957] Submitted process > HMMSEARCH (batch_13)
May-28 11:48:46.569 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fd/43a957d8d80d7b56096174a2f59569]
May-28 11:48:46.624 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:46.624 [Task submitter] INFO  nextflow.Session - [f1/307819] Submitted process > HMMSEARCH (batch_29)
May-28 11:48:46.769 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f3/5139bab5dfb71952d4b3cfc9571649]
May-28 11:48:46.791 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:46.792 [Task submitter] INFO  nextflow.Session - [24/b43c6c] Submitted process > HMMSEARCH (batch_15)
May-28 11:48:53.655 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/3078199d25a9f6537b4ed3133f5c89]
May-28 11:48:53.688 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:53.689 [Task submitter] INFO  nextflow.Session - [ca/fbc51a] Submitted process > HMMSEARCH (batch_12)
May-28 11:48:53.966 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/b43c6c84ba0b3dba30a1233760b30e]
May-28 11:48:54.012 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:48:54.013 [Task submitter] INFO  nextflow.Session - [8b/33f142] Submitted process > HMMSEARCH (batch_9)
May-28 11:49:00.754 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ca/fbc51ab2781d08c8137a9e03523c0a]
May-28 11:49:00.772 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:00.772 [Task submitter] INFO  nextflow.Session - [dc/fb9d22] Submitted process > HMMSEARCH (batch_28)
May-28 11:49:00.984 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8b/33f142dc630d9bd911d361a204d435]
May-28 11:49:01.004 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:01.005 [Task submitter] INFO  nextflow.Session - [1d/012f3e] Submitted process > HMMSEARCH (batch_30)
May-28 11:49:07.419 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dc/fb9d22daff3c9a66affdac5c5c11e8]
May-28 11:49:07.437 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:07.437 [Task submitter] INFO  nextflow.Session - [7a/f1bbbf] Submitted process > HMMSEARCH (batch_56)
May-28 11:49:07.655 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1d/012f3e6bb9c325dfb5aebc66ae3c78]
May-28 11:49:07.674 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:07.674 [Task submitter] INFO  nextflow.Session - [ec/b87c56] Submitted process > HMMSEARCH (batch_22)
May-28 11:49:15.150 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7a/f1bbbf24b9d5a3e21b3e2997c52f47]
May-28 11:49:15.173 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:15.173 [Task submitter] INFO  nextflow.Session - [ab/2a237f] Submitted process > HMMSEARCH (batch_4)
May-28 11:49:15.380 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ec/b87c56b39386aca802be43a0be4ee3]
May-28 11:49:15.399 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:15.399 [Task submitter] INFO  nextflow.Session - [c2/19c855] Submitted process > HMMSEARCH (batch_14)
May-28 11:49:22.975 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ab/2a237fc0d1fc6f60ec267e2ba34af9]
May-28 11:49:22.999 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:22.999 [Task submitter] INFO  nextflow.Session - [09/8e1b32] Submitted process > HMMSEARCH (batch_1)
May-28 11:49:23.160 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c2/19c855d18d7d5a469c4db9598681c3]
May-28 11:49:23.192 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:23.192 [Task submitter] INFO  nextflow.Session - [b2/3f86c0] Submitted process > HMMSEARCH (batch_11)
May-28 11:49:30.300 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/3f86c0ddd91b063adb4b14ccab4413]
May-28 11:49:30.314 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:30.314 [Task submitter] INFO  nextflow.Session - [c9/b867e0] Submitted process > HMMSEARCH (batch_27)
May-28 11:49:30.402 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/09/8e1b3202957d446597cb76df02cf23]
May-28 11:49:30.423 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:30.423 [Task submitter] INFO  nextflow.Session - [ee/3a5f98] Submitted process > HMMSEARCH (batch_24)
May-28 11:49:36.967 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/b867e0620828595d128c9deaa79dfa]
May-28 11:49:36.980 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:36.981 [Task submitter] INFO  nextflow.Session - [24/47cbb1] Submitted process > HMMSEARCH (batch_61)
May-28 11:49:37.384 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ee/3a5f9852529e5182a6f881de10636d]
May-28 11:49:37.397 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:37.397 [Task submitter] INFO  nextflow.Session - [b2/6903ee] Submitted process > HMMSEARCH (batch_33)
May-28 11:49:44.091 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/47cbb19c56862ba8a74a089bfcbb5a]
May-28 11:49:44.128 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:44.128 [Task submitter] INFO  nextflow.Session - [4e/ad7b24] Submitted process > HMMSEARCH (batch_38)
May-28 11:49:44.413 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/6903ee69d1c0e6c751a12e95a34347]
May-28 11:49:44.432 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:44.433 [Task submitter] INFO  nextflow.Session - [bf/6cb832] Submitted process > HMMSEARCH (batch_40)
May-28 11:49:51.002 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/ad7b248253bb63df3d5300c9d80002]
May-28 11:49:51.017 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:51.018 [Task submitter] INFO  nextflow.Session - [42/33895d] Submitted process > HMMSEARCH (batch_39)
May-28 11:49:51.314 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/6cb8326d4d29ec86451eec050a2cb3]
May-28 11:49:51.338 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:51.338 [Task submitter] INFO  nextflow.Session - [1d/0bef4f] Submitted process > HMMSEARCH (batch_42)
May-28 11:49:59.248 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/33895d7e8682e531163eef6b0b1f89]
May-28 11:49:59.276 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:59.276 [Task submitter] INFO  nextflow.Session - [3f/ec152a] Submitted process > HMMSEARCH (batch_41)
May-28 11:49:59.288 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1d/0bef4f812b0d31a9d2372f3bf63e95]
May-28 11:49:59.307 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:49:59.307 [Task submitter] INFO  nextflow.Session - [3b/2051d8] Submitted process > HMMSEARCH (batch_43)
May-28 11:50:06.313 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3b/2051d834e4a6e2ed217196a9cc132a]
May-28 11:50:06.319 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3f/ec152aaf72fca414ba7d93b8c05dae]
May-28 11:50:06.332 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:06.333 [Task submitter] INFO  nextflow.Session - [b9/c1059d] Submitted process > HMMSEARCH (batch_45)
May-28 11:50:06.358 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:06.359 [Task submitter] INFO  nextflow.Session - [4a/33a83a] Submitted process > HMMSEARCH (batch_48)
May-28 11:50:13.508 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/c1059d440e3ed2bb619aa498a8a067]
May-28 11:50:13.537 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:13.537 [Task submitter] INFO  nextflow.Session - [12/3ce0a7] Submitted process > HMMSEARCH (batch_49)
May-28 11:50:13.586 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4a/33a83a43268d2258e74d9e3bb2f894]
May-28 11:50:13.613 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:13.613 [Task submitter] INFO  nextflow.Session - [fa/cbf321] Submitted process > HMMSEARCH (batch_46)
May-28 11:50:20.459 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/3ce0a79068f3d7a26b2aa366cc2143]
May-28 11:50:20.478 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:20.478 [Task submitter] INFO  nextflow.Session - [70/172d73] Submitted process > HMMSEARCH (batch_52)
May-28 11:50:20.523 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fa/cbf321a0b7b216490f33b9b07c297c]
May-28 11:50:20.542 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:20.543 [Task submitter] INFO  nextflow.Session - [45/acf6a2] Submitted process > HMMSEARCH (batch_50)
May-28 11:50:28.651 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/45/acf6a22b58b857a46011ac348dc842]
May-28 11:50:28.687 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:28.688 [Task submitter] INFO  nextflow.Session - [c0/a0f729] Submitted process > HMMSEARCH (batch_55)
May-28 11:50:28.834 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/172d73b6c28ca4553e2d50c4c67cce]
May-28 11:50:28.853 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:28.853 [Task submitter] INFO  nextflow.Session - [e4/ba2ce3] Submitted process > HMMSEARCH (batch_57)
May-28 11:50:35.457 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/a0f72974d22bd0b632221aee7fc89b]
May-28 11:50:35.468 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:35.468 [Task submitter] INFO  nextflow.Session - [b7/9da9b2] Submitted process > HMMSEARCH (batch_58)
May-28 11:50:35.823 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e4/ba2ce3026e6594053725244d02cfc8]
May-28 11:50:35.841 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:35.841 [Task submitter] INFO  nextflow.Session - [45/0e233c] Submitted process > HMMSEARCH (batch_53)
May-28 11:50:42.571 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b7/9da9b213419f8758d46b4d85a63c4e]
May-28 11:50:42.583 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:42.584 [Task submitter] INFO  nextflow.Session - [71/fc4004] Submitted process > HMMSEARCH (batch_59)
May-28 11:50:42.826 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/45/0e233ccec961344fccdc0197c265cf]
May-28 11:50:42.845 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:42.845 [Task submitter] INFO  nextflow.Session - [6a/4df69e] Submitted process > HMMSEARCH (batch_44)
May-28 11:50:50.664 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/fc400455c6cc77406ee947a4ced343]
May-28 11:50:50.684 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:50.684 [Task submitter] INFO  nextflow.Session - [c7/bb55bf] Submitted process > HMMSEARCH (batch_51)
May-28 11:50:50.768 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/4df69e66970055fbec54846696f9b1]
May-28 11:50:50.789 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:50.790 [Task submitter] INFO  nextflow.Session - [f7/2108d8] Submitted process > HMMSEARCH (batch_35)
May-28 11:50:57.240 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f7/2108d8993adb2d8ef83174eaf62a24]
May-28 11:50:57.252 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:57.252 [Task submitter] INFO  nextflow.Session - [5f/06cb00] Submitted process > HMMSEARCH (batch_37)
May-28 11:50:57.352 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c7/bb55bf97978a8f9671cd62c20af54b]
May-28 11:50:57.364 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:50:57.364 [Task submitter] INFO  nextflow.Session - [67/c05433] Submitted process > HMMSEARCH (batch_36)
May-28 11:51:05.200 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/67/c054331bbbd8f5749c96a1a39f9596]
May-28 11:51:05.243 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:05.243 [Task submitter] INFO  nextflow.Session - [6d/439ed0] Submitted process > HMMSEARCH (batch_54)
May-28 11:51:05.746 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/06cb00532426536853f383d3dbeaef]
May-28 11:51:05.759 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:05.760 [Task submitter] INFO  nextflow.Session - [03/eacf0c] Submitted process > HMMSEARCH (batch_60)
May-28 11:51:13.508 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6d/439ed0368fa50ae194aeb236df3bba]
May-28 11:51:13.531 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:13.532 [Task submitter] INFO  nextflow.Session - [b9/53746b] Submitted process > HMMSEARCH (batch_62)
May-28 11:51:14.106 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/03/eacf0cc7143cb6ae4cf7a5aad94230]
May-28 11:51:14.124 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:14.124 [Task submitter] INFO  nextflow.Session - [9a/8361c1] Submitted process > HMMSEARCH (batch_47)
May-28 11:51:20.954 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/53746b251dcfef3d6887e72b5d3f7a]
May-28 11:51:20.976 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:20.977 [Task submitter] INFO  nextflow.Session - [71/39fe0c] Submitted process > HMMSEARCH (batch_64)
May-28 11:51:21.284 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9a/8361c1f7ca7312cbfcc0a7a79b4328]
May-28 11:51:21.300 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:21.300 [Task submitter] INFO  nextflow.Session - [7e/4f3833] Submitted process > HMMSEARCH (batch_66)
May-28 11:51:22.307 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7e/4f3833023edb5d167bce6130c30784]
May-28 11:51:22.330 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:22.330 [Task submitter] INFO  nextflow.Session - [45/ab984c] Submitted process > HMMSEARCH (batch_65)
May-28 11:51:28.298 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/39fe0cb7971408689d74173d401105]
May-28 11:51:28.312 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:28.312 [Task submitter] INFO  nextflow.Session - [b3/4145dd] Submitted process > HMMSEARCH (batch_63)
May-28 11:51:29.346 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/45/ab984c07c140188d4ccdd6e8b1c291]
May-28 11:51:29.366 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.366 [Task submitter] INFO  nextflow.Session - [cd/07a5a4] Submitted process > PROCESS_HITS (batch_16)
May-28 11:51:29.387 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.387 [Task submitter] INFO  nextflow.Session - [e8/768afd] Submitted process > PROCESS_HITS (batch_17)
May-28 11:51:29.401 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.402 [Task submitter] INFO  nextflow.Session - [5c/e5dbc6] Submitted process > PROCESS_HITS (batch_5)
May-28 11:51:29.415 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.416 [Task submitter] INFO  nextflow.Session - [78/25d719] Submitted process > PROCESS_HITS (batch_3)
May-28 11:51:29.430 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.430 [Task submitter] INFO  nextflow.Session - [b2/eb46a7] Submitted process > PROCESS_HITS (batch_25)
May-28 11:51:29.442 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.442 [Task submitter] INFO  nextflow.Session - [0d/364c71] Submitted process > PROCESS_HITS (batch_7)
May-28 11:51:29.455 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.455 [Task submitter] INFO  nextflow.Session - [c1/34990b] Submitted process > PROCESS_HITS (batch_2)
May-28 11:51:29.468 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:29.469 [Task submitter] INFO  nextflow.Session - [8a/d670e3] Submitted process > PROCESS_HITS (batch_8)
May-28 11:51:34.318 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b3/4145ddfd67df64ff4c20fd3b56180b]
May-28 11:51:34.333 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.334 [Task submitter] INFO  nextflow.Session - [c8/498a31] Submitted process > PROCESS_HITS (batch_34)
May-28 11:51:34.357 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.357 [Task submitter] INFO  nextflow.Session - [bc/6341a0] Submitted process > PROCESS_HITS (batch_19)
May-28 11:51:34.375 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.375 [Task submitter] INFO  nextflow.Session - [e9/b9bf70] Submitted process > PROCESS_HITS (batch_26)
May-28 11:51:34.388 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.389 [Task submitter] INFO  nextflow.Session - [df/a6f4c3] Submitted process > PROCESS_HITS (batch_31)
May-28 11:51:34.401 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.402 [Task submitter] INFO  nextflow.Session - [00/ad61a2] Submitted process > PROCESS_HITS (batch_23)
May-28 11:51:34.415 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.415 [Task submitter] INFO  nextflow.Session - [bc/eab976] Submitted process > PROCESS_HITS (batch_32)
May-28 11:51:34.429 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.430 [Task submitter] INFO  nextflow.Session - [c0/5405f7] Submitted process > PROCESS_HITS (batch_10)
May-28 11:51:34.446 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:34.446 [Task submitter] INFO  nextflow.Session - [49/88b588] Submitted process > PROCESS_HITS (batch_6)
May-28 11:51:43.850 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cd/07a5a40293ee0c6449b1eea6ce3089]
May-28 11:51:43.875 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:43.875 [Task submitter] INFO  nextflow.Session - [f8/500405] Submitted process > PROCESS_HITS (batch_20)
May-28 11:51:44.158 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/eb46a73cf1e6e3655fe59a2b6103f3]
May-28 11:51:44.186 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:44.186 [Task submitter] INFO  nextflow.Session - [83/452299] Submitted process > PROCESS_HITS (batch_18)
May-28 11:51:44.652 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/78/25d719e41f52b1daeb50495df4cc02]
May-28 11:51:44.667 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e8/768afd7831418498cd5e1f9cb492e5]
May-28 11:51:44.668 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:44.668 [Task submitter] INFO  nextflow.Session - [c0/3ca330] Submitted process > PROCESS_HITS (batch_13)
May-28 11:51:44.691 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:44.692 [Task submitter] INFO  nextflow.Session - [b6/00f327] Submitted process > PROCESS_HITS (batch_21)
May-28 11:51:44.699 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5c/e5dbc657d0cc2f36fb8bd71acda81c]
May-28 11:51:44.718 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:44.719 [Task submitter] INFO  nextflow.Session - [8e/c45314] Submitted process > PROCESS_HITS (batch_29)
May-28 11:51:44.757 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c1/34990be533426197aff2397cfe1011]
May-28 11:51:44.792 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:44.792 [Task submitter] INFO  nextflow.Session - [de/1c9d00] Submitted process > PROCESS_HITS (batch_15)
May-28 11:51:44.792 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0d/364c7196993f28422b37fed76b3672]
May-28 11:51:44.810 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:44.811 [Task submitter] INFO  nextflow.Session - [bd/d4e0a0] Submitted process > PROCESS_HITS (batch_12)
May-28 11:51:45.042 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8a/d670e36b89cc1f50060c39b5b64131]
May-28 11:51:45.056 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:45.057 [Task submitter] INFO  nextflow.Session - [cb/11bf56] Submitted process > PROCESS_HITS (batch_9)
May-28 11:51:49.831 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/49/88b588330d9041b163e43992d710b2]
May-28 11:51:49.867 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:49.867 [Task submitter] INFO  nextflow.Session - [29/974d0a] Submitted process > PROCESS_HITS (batch_28)
May-28 11:51:49.933 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/5405f751fbad51c892e0e9fde70032]
May-28 11:51:49.960 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:49.960 [Task submitter] INFO  nextflow.Session - [29/91f498] Submitted process > PROCESS_HITS (batch_30)
May-28 11:51:50.127 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/00/ad61a21e6d47822ad663ac5e48485f]
May-28 11:51:50.154 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:50.154 [Task submitter] INFO  nextflow.Session - [fb/6f31b3] Submitted process > PROCESS_HITS (batch_56)
May-28 11:51:50.164 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bc/eab976b492a53a41fb61b358f450fa]
May-28 11:51:50.193 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:50.193 [Task submitter] INFO  nextflow.Session - [47/5df264] Submitted process > PROCESS_HITS (batch_22)
May-28 11:51:50.401 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/a6f4c3325c8dc079e55bfbbe3eb26a]
May-28 11:51:50.429 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:50.429 [Task submitter] INFO  nextflow.Session - [cf/7f2cc9] Submitted process > PROCESS_HITS (batch_4)
May-28 11:51:50.446 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c8/498a31c67769b6a82eb97939dfbf9b]
May-28 11:51:50.473 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:50.473 [Task submitter] INFO  nextflow.Session - [e2/c20cd9] Submitted process > PROCESS_HITS (batch_14)
May-28 11:51:50.764 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bc/6341a09ff442dec5195b0f1fb51b2b]
May-28 11:51:50.789 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:50.790 [Task submitter] INFO  nextflow.Session - [24/09989a] Submitted process > PROCESS_HITS (batch_11)
May-28 11:51:50.950 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e9/b9bf708533402cf0775b43f5536f64]
May-28 11:51:50.975 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:51:50.975 [Task submitter] INFO  nextflow.Session - [bf/7bf0e0] Submitted process > PROCESS_HITS (batch_1)
May-28 11:52:01.439 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/500405e0d7fcaaabe3d40078b3e612]
May-28 11:52:01.457 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:01.457 [Task submitter] INFO  nextflow.Session - [14/22e67a] Submitted process > PROCESS_HITS (batch_27)
May-28 11:52:01.509 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/c453147cf3900231670c7e177c959c]
May-28 11:52:01.527 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:01.527 [Task submitter] INFO  nextflow.Session - [43/8c047d] Submitted process > PROCESS_HITS (batch_24)
May-28 11:52:01.564 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/3ca330b54108a540404e0bf7e8ed89]
May-28 11:52:01.590 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:01.590 [Task submitter] INFO  nextflow.Session - [42/a0d960] Submitted process > PROCESS_HITS (batch_61)
May-28 11:52:01.707 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/de/1c9d000e893d36f7e01db86ec1acc0]
May-28 11:52:01.737 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:01.738 [Task submitter] INFO  nextflow.Session - [4c/f264dd] Submitted process > PROCESS_HITS (batch_33)
May-28 11:52:01.868 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/00f327cfa9109f1a42949fcac545a5]
May-28 11:52:01.895 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:01.895 [Task submitter] INFO  nextflow.Session - [d6/c2c038] Submitted process > PROCESS_HITS (batch_38)
May-28 11:52:01.938 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/83/45229913b385e79b88e34c562039f2]
May-28 11:52:01.958 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:01.959 [Task submitter] INFO  nextflow.Session - [77/0834ad] Submitted process > PROCESS_HITS (batch_40)
May-28 11:52:01.968 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cb/11bf56e64b7769329f1d17f78bfa14]
May-28 11:52:01.997 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:01.997 [Task submitter] INFO  nextflow.Session - [ba/9f7449] Submitted process > PROCESS_HITS (batch_39)
May-28 11:52:02.047 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/d4e0a023ed9b93e00d86aa73dff85b]
May-28 11:52:02.071 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:02.071 [Task submitter] INFO  nextflow.Session - [f5/f88224] Submitted process > PROCESS_HITS (batch_42)
May-28 11:52:06.094 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/91f4981f617e0ac47c935149a3f269]
May-28 11:52:06.125 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:06.125 [Task submitter] INFO  nextflow.Session - [0e/a040be] Submitted process > PROCESS_HITS (batch_43)
May-28 11:52:06.327 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/974d0a6f39af2f600e7174e91d863f]
May-28 11:52:06.343 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:06.344 [Task submitter] INFO  nextflow.Session - [4e/1cb2a5] Submitted process > PROCESS_HITS (batch_41)
May-28 11:52:06.430 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fb/6f31b31404c6278108384d9e001619]
May-28 11:52:06.465 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:06.465 [Task submitter] INFO  nextflow.Session - [8e/fef24e] Submitted process > PROCESS_HITS (batch_45)
May-28 11:52:06.740 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e2/c20cd9ed761c9f63eaebb0f54367ac]
May-28 11:52:06.768 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:06.768 [Task submitter] INFO  nextflow.Session - [ec/84f1c2] Submitted process > PROCESS_HITS (batch_48)
May-28 11:52:06.931 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cf/7f2cc94fc7daa0af3f4eee1a29ac6f]
May-28 11:52:06.945 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/5df2640be789b6779ee40968fcfcf9]
May-28 11:52:06.970 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:06.970 [Task submitter] INFO  nextflow.Session - [95/7c46c6] Submitted process > PROCESS_HITS (batch_49)
May-28 11:52:07.005 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:07.005 [Task submitter] INFO  nextflow.Session - [6a/e9bb8a] Submitted process > PROCESS_HITS (batch_46)
May-28 11:52:07.378 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/7bf0e02b9f3768b97ec665d80c2a60]
May-28 11:52:07.406 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:07.407 [Task submitter] INFO  nextflow.Session - [f7/81a284] Submitted process > PROCESS_HITS (batch_50)
May-28 11:52:07.779 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/09989ad847500f48b962db9cdcede1]
May-28 11:52:07.805 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:07.805 [Task submitter] INFO  nextflow.Session - [0d/4009d3] Submitted process > PROCESS_HITS (batch_52)
May-28 11:52:17.130 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/43/8c047d0377511f67a46d92edc73783]
May-28 11:52:17.165 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:17.165 [Task submitter] INFO  nextflow.Session - [62/6ac389] Submitted process > PROCESS_HITS (batch_55)
May-28 11:52:17.321 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/a0d960810411d6120ae9e8bad93e9e]
May-28 11:52:17.369 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:17.370 [Task submitter] INFO  nextflow.Session - [d9/2a45ca] Submitted process > PROCESS_HITS (batch_57)
May-28 11:52:17.406 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4c/f264ddc5886a080803fac6c6dbd423]
May-28 11:52:17.437 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:17.438 [Task submitter] INFO  nextflow.Session - [8b/b70079] Submitted process > PROCESS_HITS (batch_58)
May-28 11:52:17.950 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d6/c2c038ec8056947b4557d35c5560fc]
May-28 11:52:17.971 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:17.972 [Task submitter] INFO  nextflow.Session - [d0/984ca7] Submitted process > PROCESS_HITS (batch_53)
May-28 11:52:18.072 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/9f74490a2fa11b40b9c5eb3b31faf8]
May-28 11:52:18.101 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:18.101 [Task submitter] INFO  nextflow.Session - [79/34e6d6] Submitted process > PROCESS_HITS (batch_59)
May-28 11:52:18.145 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/14/22e67af211fa9f80a9ca032f313a0b]
May-28 11:52:18.169 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:18.169 [Task submitter] INFO  nextflow.Session - [ba/cca7ad] Submitted process > PROCESS_HITS (batch_44)
May-28 11:52:18.544 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/77/0834ad680da562228d9835319c44ff]
May-28 11:52:18.574 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:18.574 [Task submitter] INFO  nextflow.Session - [fb/9e4ebc] Submitted process > PROCESS_HITS (batch_35)
May-28 11:52:19.053 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/f882242e014befdd929d4c7f69712e]
May-28 11:52:19.104 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:19.104 [Task submitter] INFO  nextflow.Session - [b1/a20f37] Submitted process > PROCESS_HITS (batch_51)
May-28 11:52:23.089 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/1cb2a56bc553df5c300b58cba3c02a]
May-28 11:52:23.116 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:23.116 [Task submitter] INFO  nextflow.Session - [e7/c09aac] Submitted process > PROCESS_HITS (batch_36)
May-28 11:52:23.216 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/95/7c46c69e771b07b0692351acdc85ab]
May-28 11:52:23.241 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:23.241 [Task submitter] INFO  nextflow.Session - [0a/ccf8de] Submitted process > PROCESS_HITS (batch_37)
May-28 11:52:23.441 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0e/a040bedea602a97192c59506e6abbf]
May-28 11:52:23.472 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:23.472 [Task submitter] INFO  nextflow.Session - [2b/806b98] Submitted process > PROCESS_HITS (batch_54)
May-28 11:52:23.531 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/fef24ed16433ed1811b49af917e621]
May-28 11:52:23.563 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:23.563 [Task submitter] INFO  nextflow.Session - [61/155446] Submitted process > PROCESS_HITS (batch_60)
May-28 11:52:23.631 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f7/81a2844236bd2c84983071ab971953]
May-28 11:52:23.664 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:23.664 [Task submitter] INFO  nextflow.Session - [57/79f82c] Submitted process > PROCESS_HITS (batch_62)
May-28 11:52:24.135 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0d/4009d3a986e8df5050f4c1c683e9b5]
May-28 11:52:24.152 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:24.153 [Task submitter] INFO  nextflow.Session - [df/0f13ea] Submitted process > PROCESS_HITS (batch_47)
May-28 11:52:24.247 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/e9bb8afa931f51894dbdb36044aec9]
May-28 11:52:24.267 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:24.267 [Task submitter] INFO  nextflow.Session - [30/240e35] Submitted process > PROCESS_HITS (batch_66)
May-28 11:52:24.293 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ec/84f1c20434b5cac640cb9db848b997]
May-28 11:52:24.317 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:24.317 [Task submitter] INFO  nextflow.Session - [71/1046b5] Submitted process > PROCESS_HITS (batch_64)
May-28 11:52:26.519 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/30/240e35bade1e5f41f3be6bca0634aa]
May-28 11:52:26.537 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:26.537 [Task submitter] INFO  nextflow.Session - [8a/8ac1f0] Submitted process > PROCESS_HITS (batch_65)
May-28 11:52:32.563 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d9/2a45cabbe3aefe5c2bf8f836143c37]
May-28 11:52:32.582 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 11:52:32.582 [Task submitter] INFO  nextflow.Session - [de/06c05e] Submitted process > PROCESS_HITS (batch_63)
May-28 11:52:33.105 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8b/b70079e784543d7b17fa5938f9d03f]
May-28 11:52:33.188 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/62/6ac389ec37af4c231eae38f733c14d]
May-28 11:52:33.409 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d0/984ca77ca82f9eff402b1599544a74]
May-28 11:52:33.446 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fb/9e4ebcb777953521bd5b19ca6ecf6e]
May-28 11:52:33.746 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 12 -- submitted tasks are shown below
~> TaskHandler[id: 120; name: PROCESS_HITS (batch_59); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/34e6d6a8dfb21c9f1f8e1da4b58520]
~> TaskHandler[id: 121; name: PROCESS_HITS (batch_44); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/cca7ad42ea7029faf17c74b661a6d1]
~> TaskHandler[id: 123; name: PROCESS_HITS (batch_51); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/a20f374667da83c38a37a54c7dfa4d]
~> TaskHandler[id: 124; name: PROCESS_HITS (batch_36); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e7/c09aacb49b2d6ca0ac19a37b003261]
~> TaskHandler[id: 125; name: PROCESS_HITS (batch_37); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/ccf8de90cf4656f8d1a9d9d7cbb6f2]
~> TaskHandler[id: 126; name: PROCESS_HITS (batch_54); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/806b981dd4c2e2e2ceb26ce958707c]
~> TaskHandler[id: 127; name: PROCESS_HITS (batch_60); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/155446b9890a2331864d30e4d1d5b0]
~> TaskHandler[id: 128; name: PROCESS_HITS (batch_62); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/79f82c5c8b02444d36cc94ad71e0e1]
~> TaskHandler[id: 129; name: PROCESS_HITS (batch_47); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/0f13ea562fc1e7e47537fc8616af61]
~> TaskHandler[id: 131; name: PROCESS_HITS (batch_64); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/1046b59dd99813c2096584355e5f38]
.. remaining tasks omitted.
May-28 11:52:34.134 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/34e6d6a8dfb21c9f1f8e1da4b58520]
May-28 11:52:34.160 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/a20f374667da83c38a37a54c7dfa4d]
May-28 11:52:34.227 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/cca7ad42ea7029faf17c74b661a6d1]
May-28 11:52:37.399 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e7/c09aacb49b2d6ca0ac19a37b003261]
May-28 11:52:37.602 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0a/ccf8de90cf4656f8d1a9d9d7cbb6f2]
May-28 11:52:37.695 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/155446b9890a2331864d30e4d1d5b0]
May-28 11:52:38.197 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/79f82c5c8b02444d36cc94ad71e0e1]
May-28 11:52:38.723 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/71/1046b59dd99813c2096584355e5f38]
May-28 11:52:38.938 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/0f13ea562fc1e7e47537fc8616af61]
May-28 11:52:38.976 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/806b981dd4c2e2e2ceb26ce958707c]
May-28 11:52:41.081 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8a/8ac1f07a25af52c61554e095ed3944]
May-28 11:52:47.045 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/de/06c05ef5c65f079935d1549da45d15]
May-28 11:52:47.181 [Actor Thread 4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME (1); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Not a valid path value type: java.lang.Integer (16)
May-28 11:52:47.189 [Actor Thread 4] ERROR nextflow.processor.TaskProcessor - Error executing process > 'CREATE_DATAFRAME (1)'

Caused by:
  Not a valid path value type: java.lang.Integer (16)



Container:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/containers/hmmer.sif

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
May-28 11:52:47.192 [Actor Thread 4] DEBUG nextflow.Session - Session aborted -- Cause: Not a valid path value type: java.lang.Integer (16)
May-28 11:52:47.203 [Actor Thread 4] DEBUG nextflow.Session - The following nodes are still active:
[process] CREATE_DATAFRAME
  status=ACTIVE
  port 0: (value) bound ; channel: csv_files
  port 1: (queue) closed; channel: classification
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: batch_count
  port 4: (cntrl) -     ; channel: $

[process] AUTOENCODER
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: input_csv
  port 1: (queue) OPEN  ; channel: scaler
  port 2: (queue) OPEN  ; channel: model
  port 3: (queue) OPEN  ; channel: script
  port 4: (cntrl) -     ; channel: $

[process] MODEL_PREDICT
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: encoded_features
  port 1: (queue) OPEN  ; channel: ml_model
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: version
  port 4: (cntrl) -     ; channel: $

May-28 11:52:47.206 [main] DEBUG nextflow.Session - Session await > all processes finished
May-28 11:52:47.206 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-28 11:52:47.207 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-28 11:52:47.208 [Actor Thread 10] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
May-28 11:52:47.225 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=133; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=2h 35m 53s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=19; peakCpus=38; peakMemory=76 GB; ]
May-28 11:52:47.418 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-28 11:52:47.444 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
