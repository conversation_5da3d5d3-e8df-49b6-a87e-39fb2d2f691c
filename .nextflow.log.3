Jun-01 20:58:34.851 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
Jun-01 20:58:34.969 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
Jun-01 20:58:34.989 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
Jun-01 20:58:35.012 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-01 20:58:35.014 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-01 20:58:35.017 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-01 20:58:35.031 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-01 20:58:35.051 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-01 20:58:35.053 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
Jun-01 20:58:35.090 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
Jun-01 20:58:35.093 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@9f6e406] - activable => nextflow.secret.LocalSecretsProvider@9f6e406
Jun-01 20:58:35.105 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Jun-01 20:58:35.591 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-01 20:58:35.606 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [berserk_ekeblad] DSL2 - revision: e9019381d3
Jun-01 20:58:35.608 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-01 20:58:35.609 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-01 20:58:35.657 [main] DEBUG nextflow.Session - Session UUID: *************-49d0-8758-46b8e122ccbd
Jun-01 20:58:35.657 [main] DEBUG nextflow.Session - Run name: berserk_ekeblad
Jun-01 20:58:35.659 [main] DEBUG nextflow.Session - Executor pool size: 32
Jun-01 20:58:35.668 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-01 20:58:35.672 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 20:58:35.692 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (113.5 GB) - Swap: 8 GB (468 KB)
Jun-01 20:58:35.728 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
Jun-01 20:58:35.763 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-01 20:58:35.776 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-01 20:58:35.781 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-01 20:58:35.804 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-01 20:58:35.812 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
Jun-01 20:58:35.942 [main] DEBUG nextflow.Session - Session start
Jun-01 20:58:36.606 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-01 20:58:36.645 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

Jun-01 20:58:36.755 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
Jun-01 20:58:36.765 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 20:58:36.766 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 20:58:36.771 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-01 20:58:36.777 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
Jun-01 20:58:36.779 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-01 20:58:36.799 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
Jun-01 20:58:36.862 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
Jun-01 20:58:36.879 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 20:58:36.880 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 20:58:36.882 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
Jun-01 20:58:36.883 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
Jun-01 20:58:36.898 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
Jun-01 20:58:36.900 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 20:58:36.900 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 20:58:36.903 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
Jun-01 20:58:36.907 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
Jun-01 20:58:36.932 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
Jun-01 20:58:36.934 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 20:58:36.935 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 20:58:36.937 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
Jun-01 20:58:36.948 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 20:58:36.948 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 20:58:36.950 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
Jun-01 20:58:36.956 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
Jun-01 20:58:36.957 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-01 20:58:36.959 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
Jun-01 20:58:36.964 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
Jun-01 20:58:36.967 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
Jun-01 20:58:36.973 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
Jun-01 20:58:36.974 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-01 20:58:36.975 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
Jun-01 20:58:36.976 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-01 20:58:36.977 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
Jun-01 20:58:36.978 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
Jun-01 20:58:36.980 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
Jun-01 20:58:36.981 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
Jun-01 20:58:36.987 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_1343ce0d68727479: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
Jun-01 20:58:36.987 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-01 20:58:36.988 [main] DEBUG nextflow.Session - Session await
Jun-01 20:58:37.117 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:58:37.119 [Task submitter] INFO  nextflow.Session - [97/637297] Submitted process > PRODIGAL (1)
Jun-01 20:58:44.323 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/63729742627f8d2fe7547576fc437b]
Jun-01 20:58:44.324 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 20:58:44.350 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-01 20:58:44.396 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:58:44.397 [Task submitter] INFO  nextflow.Session - [bb/1654cf] Submitted process > HMMSEARCH (batch_31)
Jun-01 20:58:44.430 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:58:44.431 [Task submitter] INFO  nextflow.Session - [2e/183c81] Submitted process > HMMSEARCH (batch_25)
Jun-01 20:58:51.094 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bb/1654cf418af0cf4a8c484b15c30acd]
Jun-01 20:58:51.111 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:58:51.112 [Task submitter] INFO  nextflow.Session - [1f/2a7cce] Submitted process > HMMSEARCH (batch_20)
Jun-01 20:58:51.556 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2e/183c8109f296059eac7c76ceccc42d]
Jun-01 20:58:51.575 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:58:51.575 [Task submitter] INFO  nextflow.Session - [89/1350a9] Submitted process > HMMSEARCH (batch_3)
Jun-01 20:58:58.705 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1f/2a7cceade6f65c23d3ff84b0b73746]
Jun-01 20:58:58.721 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:58:58.722 [Task submitter] INFO  nextflow.Session - [21/14dc65] Submitted process > HMMSEARCH (batch_22)
Jun-01 20:58:58.932 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/89/1350a9ef7a78de2ea3f43675f6d723]
Jun-01 20:58:58.941 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:58:58.949 [Task submitter] INFO  nextflow.Session - [86/4f4f44] Submitted process > HMMSEARCH (batch_2)
Jun-01 20:59:05.343 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/21/14dc650fd70267a15c7ae26a565afd]
Jun-01 20:59:05.359 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:05.359 [Task submitter] INFO  nextflow.Session - [c6/7df0e9] Submitted process > HMMSEARCH (batch_7)
Jun-01 20:59:05.681 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/86/4f4f445850ffe8fec80f35fe9deb4f]
Jun-01 20:59:05.694 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:05.694 [Task submitter] INFO  nextflow.Session - [bb/45d1dd] Submitted process > HMMSEARCH (batch_24)
Jun-01 20:59:12.194 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c6/7df0e98afecec5262ab23e9ae52c96]
Jun-01 20:59:12.205 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:12.206 [Task submitter] INFO  nextflow.Session - [6a/16a800] Submitted process > HMMSEARCH (batch_27)
Jun-01 20:59:12.505 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bb/45d1dda5a4144e172b0a247e8e5a63]
Jun-01 20:59:12.559 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:12.560 [Task submitter] INFO  nextflow.Session - [6a/3b5d40] Submitted process > HMMSEARCH (batch_11)
Jun-01 20:59:19.164 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/3b5d402bf4828707a617da69332ba4]
Jun-01 20:59:19.178 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:19.179 [Task submitter] INFO  nextflow.Session - [8b/d92f8c] Submitted process > HMMSEARCH (batch_19)
Jun-01 20:59:19.202 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/16a800a0dc77e3709cb7c60b0f384b]
Jun-01 20:59:19.215 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:19.215 [Task submitter] INFO  nextflow.Session - [3f/5364dc] Submitted process > HMMSEARCH (batch_5)
Jun-01 20:59:25.504 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8b/d92f8c69eb31c9750220d0cbe7e07e]
Jun-01 20:59:25.517 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:25.518 [Task submitter] INFO  nextflow.Session - [0b/b221b0] Submitted process > HMMSEARCH (batch_18)
Jun-01 20:59:25.590 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3f/5364dc536326f495ddb168058ec1aa]
Jun-01 20:59:25.606 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:25.607 [Task submitter] INFO  nextflow.Session - [12/1c5eb2] Submitted process > HMMSEARCH (batch_6)
Jun-01 20:59:31.996 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/1c5eb29f60142102cd0a14b26d2e08]
Jun-01 20:59:32.020 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:32.021 [Task submitter] INFO  nextflow.Session - [6a/0777d5] Submitted process > HMMSEARCH (batch_40)
Jun-01 20:59:32.317 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0b/b221b0ed0ba86bcd336d090804c6da]
Jun-01 20:59:32.328 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:32.328 [Task submitter] INFO  nextflow.Session - [87/d2ab2b] Submitted process > HMMSEARCH (batch_1)
Jun-01 20:59:38.452 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/0777d5498739b2a4e3db8eb5b00f3a]
Jun-01 20:59:38.465 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:38.465 [Task submitter] INFO  nextflow.Session - [61/679b62] Submitted process > HMMSEARCH (batch_17)
Jun-01 20:59:38.869 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/87/d2ab2bca19b8efdde31bb408162c25]
Jun-01 20:59:38.888 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:38.888 [Task submitter] INFO  nextflow.Session - [a7/e70ec0] Submitted process > HMMSEARCH (batch_9)
Jun-01 20:59:44.817 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/679b628dffaf4a35fcfdb6f7aa4492]
Jun-01 20:59:44.843 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:44.843 [Task submitter] INFO  nextflow.Session - [7f/7c57b2] Submitted process > HMMSEARCH (batch_28)
Jun-01 20:59:45.497 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a7/e70ec07d2263dba1f4bb1389efd7fc]
Jun-01 20:59:45.519 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:45.520 [Task submitter] INFO  nextflow.Session - [ee/5faab1] Submitted process > HMMSEARCH (batch_21)
Jun-01 20:59:51.544 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/7c57b24bf5ada47df495d15ae9da55]
Jun-01 20:59:51.572 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:51.572 [Task submitter] INFO  nextflow.Session - [77/b5361f] Submitted process > HMMSEARCH (batch_15)
Jun-01 20:59:52.223 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ee/5faab1762d9270defb1374d47d6593]
Jun-01 20:59:52.235 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:52.235 [Task submitter] INFO  nextflow.Session - [c2/216738] Submitted process > HMMSEARCH (batch_16)
Jun-01 20:59:58.407 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/77/b5361f1fe3a2e1c7feaf73772a7eac]
Jun-01 20:59:58.428 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:58.428 [Task submitter] INFO  nextflow.Session - [a9/771f45] Submitted process > HMMSEARCH (batch_30)
Jun-01 20:59:59.020 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c2/21673843d6b464978bfaac74b2318c]
Jun-01 20:59:59.037 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 20:59:59.037 [Task submitter] INFO  nextflow.Session - [c2/9d8049] Submitted process > HMMSEARCH (batch_23)
Jun-01 21:00:05.224 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a9/771f4546eba791f97b391923d02a50]
Jun-01 21:00:05.243 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:05.243 [Task submitter] INFO  nextflow.Session - [19/e5818c] Submitted process > HMMSEARCH (batch_4)
Jun-01 21:00:05.872 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c2/9d8049334e8002116511a9354f0df5]
Jun-01 21:00:05.882 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:05.883 [Task submitter] INFO  nextflow.Session - [dd/240b6a] Submitted process > HMMSEARCH (batch_14)
Jun-01 21:00:12.389 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/19/e5818cf70bbb3dfc7fde059be23b85]
Jun-01 21:00:12.410 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:12.410 [Task submitter] INFO  nextflow.Session - [65/2d1562] Submitted process > HMMSEARCH (batch_10)
Jun-01 21:00:12.987 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dd/240b6abf43ba7400b904c3ec7c7d86]
Jun-01 21:00:13.008 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:13.008 [Task submitter] INFO  nextflow.Session - [ee/4a2e97] Submitted process > HMMSEARCH (batch_54)
Jun-01 21:00:19.099 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/65/2d15620dda547a0677fb7002b89f4c]
Jun-01 21:00:19.124 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:19.124 [Task submitter] INFO  nextflow.Session - [0c/17d36e] Submitted process > HMMSEARCH (batch_13)
Jun-01 21:00:19.854 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ee/4a2e97027883c9a2321828e9ca9d13]
Jun-01 21:00:19.873 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:19.873 [Task submitter] INFO  nextflow.Session - [d9/cc4118] Submitted process > HMMSEARCH (batch_32)
Jun-01 21:00:26.189 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0c/17d36e2df4702d6f27fe1c38e25452]
Jun-01 21:00:26.208 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:26.209 [Task submitter] INFO  nextflow.Session - [f2/fe31a1] Submitted process > HMMSEARCH (batch_26)
Jun-01 21:00:26.795 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d9/cc4118f6a6b1a508c563eb39550ab1]
Jun-01 21:00:26.809 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:26.809 [Task submitter] INFO  nextflow.Session - [eb/814dda] Submitted process > HMMSEARCH (batch_8)
Jun-01 21:00:33.215 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f2/fe31a1f1799d145fb40a5bea7e2096]
Jun-01 21:00:33.227 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:33.227 [Task submitter] INFO  nextflow.Session - [6c/6e6b1a] Submitted process > HMMSEARCH (batch_29)
Jun-01 21:00:33.542 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/eb/814dda7c4f00fb339c5a91866fbcc2]
Jun-01 21:00:33.558 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:33.558 [Task submitter] INFO  nextflow.Session - [dd/767bf2] Submitted process > HMMSEARCH (batch_65)
Jun-01 21:00:39.686 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6c/6e6b1a0c369f895d126c301017ef8d]
Jun-01 21:00:39.698 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:39.698 [Task submitter] INFO  nextflow.Session - [80/7d7672] Submitted process > HMMSEARCH (batch_41)
Jun-01 21:00:40.030 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dd/767bf2e7da199f24389800d45435e0]
Jun-01 21:00:40.044 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:40.044 [Task submitter] INFO  nextflow.Session - [17/f650ca] Submitted process > HMMSEARCH (batch_57)
Jun-01 21:00:45.835 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/80/7d76720ae0b9f6624cfd80e1a30ce3]
Jun-01 21:00:45.852 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:45.853 [Task submitter] INFO  nextflow.Session - [dc/a9056e] Submitted process > HMMSEARCH (batch_12)
Jun-01 21:00:46.533 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/f650cae06a202bd80e6bf5c0aa0fc7]
Jun-01 21:00:46.548 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:46.548 [Task submitter] INFO  nextflow.Session - [55/cbe46f] Submitted process > HMMSEARCH (batch_44)
Jun-01 21:00:52.249 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dc/a9056e7cf46447e8c1215669208fae]
Jun-01 21:00:52.261 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:52.261 [Task submitter] INFO  nextflow.Session - [4f/205fbf] Submitted process > HMMSEARCH (batch_49)
Jun-01 21:00:52.766 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/55/cbe46fabf8aa219fdcb68f908122d5]
Jun-01 21:00:52.787 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:52.787 [Task submitter] INFO  nextflow.Session - [8d/541b79] Submitted process > HMMSEARCH (batch_45)
Jun-01 21:00:58.951 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4f/205fbf8ae854f69b3430ae97ae12b3]
Jun-01 21:00:58.966 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:58.967 [Task submitter] INFO  nextflow.Session - [6c/0b160a] Submitted process > HMMSEARCH (batch_62)
Jun-01 21:00:59.501 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8d/541b799e8a9ed34ef5539ad2aa966b]
Jun-01 21:00:59.511 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:00:59.512 [Task submitter] INFO  nextflow.Session - [b0/27ea76] Submitted process > HMMSEARCH (batch_58)
Jun-01 21:01:05.810 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6c/0b160a44c584eca317184b3ea6aff2]
Jun-01 21:01:05.821 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:05.821 [Task submitter] INFO  nextflow.Session - [05/f7a239] Submitted process > HMMSEARCH (batch_42)
Jun-01 21:01:06.116 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b0/27ea76d250728a8926e7ea19aadf70]
Jun-01 21:01:06.126 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:06.126 [Task submitter] INFO  nextflow.Session - [8f/4ef175] Submitted process > HMMSEARCH (batch_50)
Jun-01 21:01:12.086 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/05/f7a23957fa6667efa572523853f0a5]
Jun-01 21:01:12.096 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:12.097 [Task submitter] INFO  nextflow.Session - [5a/222bf3] Submitted process > HMMSEARCH (batch_35)
Jun-01 21:01:12.530 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8f/4ef1759fbb04ec4c6b3f1d13b74036]
Jun-01 21:01:12.552 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:12.552 [Task submitter] INFO  nextflow.Session - [54/b32bbf] Submitted process > HMMSEARCH (batch_61)
Jun-01 21:01:17.595 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/222bf3e484b6d1b0e5c380f1d94947]
Jun-01 21:01:17.607 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:17.607 [Task submitter] INFO  nextflow.Session - [7d/ebf21e] Submitted process > HMMSEARCH (batch_48)
Jun-01 21:01:18.082 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/54/b32bbf349652b07df8c459617f033e]
Jun-01 21:01:18.093 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:18.094 [Task submitter] INFO  nextflow.Session - [84/22d1a9] Submitted process > HMMSEARCH (batch_43)
Jun-01 21:01:23.361 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7d/ebf21e9406bc5ed8ecde4c168212c7]
Jun-01 21:01:23.376 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:23.376 [Task submitter] INFO  nextflow.Session - [cd/8f1668] Submitted process > HMMSEARCH (batch_51)
Jun-01 21:01:23.866 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/22d1a9f4f855e641d4a30b99206a6c]
Jun-01 21:01:23.876 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:23.876 [Task submitter] INFO  nextflow.Session - [e1/898415] Submitted process > HMMSEARCH (batch_63)
Jun-01 21:01:29.718 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cd/8f1668cb7b251cb915f7aff7480603]
Jun-01 21:01:29.735 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:29.735 [Task submitter] INFO  nextflow.Session - [8f/3603e8] Submitted process > HMMSEARCH (batch_39)
Jun-01 21:01:30.431 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/8984155491569ca4f5d03ef8941418]
Jun-01 21:01:30.442 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:30.442 [Task submitter] INFO  nextflow.Session - [a1/d86785] Submitted process > HMMSEARCH (batch_55)
Jun-01 21:01:36.142 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8f/3603e8a625b506b8d461faafbcaaf7]
Jun-01 21:01:36.155 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:36.155 [Task submitter] INFO  nextflow.Session - [a5/687918] Submitted process > HMMSEARCH (batch_37)
Jun-01 21:01:36.617 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/d8678577eaf8f21eb7e8700a80e864]
Jun-01 21:01:36.629 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:36.630 [Task submitter] INFO  nextflow.Session - [cf/0869a4] Submitted process > HMMSEARCH (batch_53)
Jun-01 21:01:42.609 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/687918e5f11c5e434cd640e83c171e]
Jun-01 21:01:42.626 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:42.627 [Task submitter] INFO  nextflow.Session - [73/41db0e] Submitted process > HMMSEARCH (batch_33)
Jun-01 21:01:43.211 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cf/0869a45aa91cfe3aa3cb8aa7bdf5c0]
Jun-01 21:01:43.224 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:43.224 [Task submitter] INFO  nextflow.Session - [52/13b6d4] Submitted process > HMMSEARCH (batch_36)
Jun-01 21:01:48.692 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/73/41db0e7bc5c99ad3670b44d9b5c69f]
Jun-01 21:01:48.722 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:48.722 [Task submitter] INFO  nextflow.Session - [95/70a864] Submitted process > HMMSEARCH (batch_38)
Jun-01 21:01:49.880 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/13b6d4791cca9286643064701b4189]
Jun-01 21:01:49.890 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:49.890 [Task submitter] INFO  nextflow.Session - [c3/e3954f] Submitted process > HMMSEARCH (batch_56)
Jun-01 21:01:55.356 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/95/70a864c49ddaa636d190633195a747]
Jun-01 21:01:55.370 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:55.370 [Task submitter] INFO  nextflow.Session - [2d/babc6a] Submitted process > HMMSEARCH (batch_47)
Jun-01 21:01:56.295 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c3/e3954f664527b7e94aa487ae6859f3]
Jun-01 21:01:56.310 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:01:56.310 [Task submitter] INFO  nextflow.Session - [a0/ea38e6] Submitted process > HMMSEARCH (batch_59)
Jun-01 21:02:02.001 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2d/babc6a3a0e0b55ad65bc02aba9ca51]
Jun-01 21:02:02.028 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:02.028 [Task submitter] INFO  nextflow.Session - [22/3009c5] Submitted process > HMMSEARCH (batch_60)
Jun-01 21:02:02.982 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a0/ea38e6c62d5db68d2df5106b3a1855]
Jun-01 21:02:03.008 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:03.009 [Task submitter] INFO  nextflow.Session - [cf/1fb48c] Submitted process > HMMSEARCH (batch_46)
Jun-01 21:02:08.894 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/22/3009c50836bbb38dad643f1832787f]
Jun-01 21:02:08.907 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:08.907 [Task submitter] INFO  nextflow.Session - [15/e5ba0a] Submitted process > HMMSEARCH (batch_34)
Jun-01 21:02:09.712 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cf/1fb48c43f392236874395ce86c0b92]
Jun-01 21:02:09.740 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:09.741 [Task submitter] INFO  nextflow.Session - [43/35a37c] Submitted process > HMMSEARCH (batch_52)
Jun-01 21:02:15.414 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/e5ba0af57a0ebd23bbcfe61723938a]
Jun-01 21:02:15.429 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:15.430 [Task submitter] INFO  nextflow.Session - [7a/4915b9] Submitted process > HMMSEARCH (batch_64)
Jun-01 21:02:16.616 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/43/35a37c953bc0779414280da321140d]
Jun-01 21:02:16.627 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:16.627 [Task submitter] INFO  nextflow.Session - [6b/493a06] Submitted process > HMMSEARCH (batch_66)
Jun-01 21:02:17.680 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/493a0627837a8f2cb91706f441630e]
Jun-01 21:02:17.695 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.695 [Task submitter] INFO  nextflow.Session - [c5/f94c83] Submitted process > PROCESS_HITS (batch_31)
Jun-01 21:02:17.707 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.707 [Task submitter] INFO  nextflow.Session - [88/612fa8] Submitted process > PROCESS_HITS (batch_25)
Jun-01 21:02:17.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.728 [Task submitter] INFO  nextflow.Session - [41/f75523] Submitted process > PROCESS_HITS (batch_20)
Jun-01 21:02:17.740 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.740 [Task submitter] INFO  nextflow.Session - [c1/c76f55] Submitted process > PROCESS_HITS (batch_3)
Jun-01 21:02:17.751 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.751 [Task submitter] INFO  nextflow.Session - [a4/4c8951] Submitted process > PROCESS_HITS (batch_22)
Jun-01 21:02:17.772 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.772 [Task submitter] INFO  nextflow.Session - [f5/94d9d2] Submitted process > PROCESS_HITS (batch_2)
Jun-01 21:02:17.786 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.786 [Task submitter] INFO  nextflow.Session - [32/e0badf] Submitted process > PROCESS_HITS (batch_7)
Jun-01 21:02:17.806 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:17.806 [Task submitter] INFO  nextflow.Session - [08/0c203f] Submitted process > PROCESS_HITS (batch_24)
Jun-01 21:02:22.187 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7a/4915b9e6a1a7d4146b01bf350905f7]
Jun-01 21:02:22.203 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.203 [Task submitter] INFO  nextflow.Session - [84/fafa29] Submitted process > PROCESS_HITS (batch_11)
Jun-01 21:02:22.218 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.218 [Task submitter] INFO  nextflow.Session - [5d/333553] Submitted process > PROCESS_HITS (batch_27)
Jun-01 21:02:22.245 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.245 [Task submitter] INFO  nextflow.Session - [c3/2345e9] Submitted process > PROCESS_HITS (batch_19)
Jun-01 21:02:22.259 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.259 [Task submitter] INFO  nextflow.Session - [4e/3a036c] Submitted process > PROCESS_HITS (batch_5)
Jun-01 21:02:22.273 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.273 [Task submitter] INFO  nextflow.Session - [6a/896d2d] Submitted process > PROCESS_HITS (batch_6)
Jun-01 21:02:22.287 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.287 [Task submitter] INFO  nextflow.Session - [ba/11f556] Submitted process > PROCESS_HITS (batch_18)
Jun-01 21:02:22.314 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.314 [Task submitter] INFO  nextflow.Session - [59/e6896e] Submitted process > PROCESS_HITS (batch_40)
Jun-01 21:02:22.327 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:22.327 [Task submitter] INFO  nextflow.Session - [24/458eaa] Submitted process > PROCESS_HITS (batch_1)
Jun-01 21:02:31.305 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/f94c8386e894079cac438e3f05d11c]
Jun-01 21:02:31.327 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:31.328 [Task submitter] INFO  nextflow.Session - [b8/831852] Submitted process > PROCESS_HITS (batch_17)
Jun-01 21:02:31.384 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/88/612fa8bbf9f0a78c9b608cc2407910]
Jun-01 21:02:31.404 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:31.404 [Task submitter] INFO  nextflow.Session - [5b/408e53] Submitted process > PROCESS_HITS (batch_9)
Jun-01 21:02:31.412 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/08/0c203f4d04d05e1a76f6e9f20cc494]
Jun-01 21:02:31.429 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:31.429 [Task submitter] INFO  nextflow.Session - [aa/31e0a8] Submitted process > PROCESS_HITS (batch_28)
Jun-01 21:02:31.540 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/94d9d25eb2ae0bbfe8ec0bfca5a369]
Jun-01 21:02:31.564 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:31.564 [Task submitter] INFO  nextflow.Session - [a1/b553ef] Submitted process > PROCESS_HITS (batch_21)
Jun-01 21:02:31.610 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c1/c76f5513857c118ec9f727479f9b99]
Jun-01 21:02:31.610 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a4/4c895110f42b873dccca3633bdf059]
Jun-01 21:02:31.630 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:31.631 [Task submitter] INFO  nextflow.Session - [54/fc7ada] Submitted process > PROCESS_HITS (batch_15)
Jun-01 21:02:31.653 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:31.654 [Task submitter] INFO  nextflow.Session - [c3/58a2fc] Submitted process > PROCESS_HITS (batch_16)
Jun-01 21:02:31.806 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/41/f75523b3af00daf1ff737297e202fc]
Jun-01 21:02:31.826 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:31.826 [Task submitter] INFO  nextflow.Session - [c4/1743ea] Submitted process > PROCESS_HITS (batch_30)
Jun-01 21:02:32.284 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/32/e0badf284efde3b90f67d38512c561]
Jun-01 21:02:32.302 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:32.302 [Task submitter] INFO  nextflow.Session - [94/1ceafc] Submitted process > PROCESS_HITS (batch_23)
Jun-01 21:02:35.114 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5d/3335539986ca48ccb4aea8c3024565]
Jun-01 21:02:35.144 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:35.145 [Task submitter] INFO  nextflow.Session - [49/0ce379] Submitted process > PROCESS_HITS (batch_4)
Jun-01 21:02:35.258 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6a/896d2dad5a1e8b984319c613569439]
Jun-01 21:02:35.276 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:35.277 [Task submitter] INFO  nextflow.Session - [24/e4c71a] Submitted process > PROCESS_HITS (batch_14)
Jun-01 21:02:35.428 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/3a036c66cbf22b22b0a74ac808153d]
Jun-01 21:02:35.445 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:35.445 [Task submitter] INFO  nextflow.Session - [d3/73b5fb] Submitted process > PROCESS_HITS (batch_10)
Jun-01 21:02:35.655 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/59/e6896e52552da1f5a203b9a6c2d7a1]
Jun-01 21:02:35.672 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:35.672 [Task submitter] INFO  nextflow.Session - [48/199160] Submitted process > PROCESS_HITS (batch_54)
Jun-01 21:02:35.817 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/458eaaa1265986127a1944d2a02af6]
Jun-01 21:02:35.835 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:35.835 [Task submitter] INFO  nextflow.Session - [a6/0804c5] Submitted process > PROCESS_HITS (batch_13)
Jun-01 21:02:35.948 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c3/2345e9ae7b8514e670d47b9e94dfec]
Jun-01 21:02:35.965 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:35.966 [Task submitter] INFO  nextflow.Session - [f8/b27661] Submitted process > PROCESS_HITS (batch_32)
Jun-01 21:02:35.967 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/fafa29ad6dbe508c04213ac01797ab]
Jun-01 21:02:35.999 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:35.999 [Task submitter] INFO  nextflow.Session - [8d/455875] Submitted process > PROCESS_HITS (batch_26)
Jun-01 21:02:36.195 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ba/11f55654c15e76a0095f4d4bf6a609]
Jun-01 21:02:36.207 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:36.207 [Task submitter] INFO  nextflow.Session - [da/543cad] Submitted process > PROCESS_HITS (batch_8)
Jun-01 21:02:43.370 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/aa/31e0a88e053d7882f60a0a2d28b7f1]
Jun-01 21:02:43.388 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:43.388 [Task submitter] INFO  nextflow.Session - [0b/43e938] Submitted process > PROCESS_HITS (batch_29)
Jun-01 21:02:43.485 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b8/831852b1f21a653e187cfe43de9d48]
Jun-01 21:02:43.509 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:43.510 [Task submitter] INFO  nextflow.Session - [2a/c8dc44] Submitted process > PROCESS_HITS (batch_65)
Jun-01 21:02:43.696 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5b/408e53bfa508799584b1c644669078]
Jun-01 21:02:43.731 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:43.732 [Task submitter] INFO  nextflow.Session - [16/25c965] Submitted process > PROCESS_HITS (batch_41)
Jun-01 21:02:43.740 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/54/fc7ada647ccd332b9e2d88a857522a]
Jun-01 21:02:43.756 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:43.756 [Task submitter] INFO  nextflow.Session - [99/bc8b23] Submitted process > PROCESS_HITS (batch_57)
Jun-01 21:02:43.799 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c3/58a2fcea3077fb2363a7ccbd7785c4]
Jun-01 21:02:43.815 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:43.815 [Task submitter] INFO  nextflow.Session - [66/847bbf] Submitted process > PROCESS_HITS (batch_12)
Jun-01 21:02:43.834 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a1/b553ef4990d49b671f65f3a6189167]
Jun-01 21:02:43.858 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:43.858 [Task submitter] INFO  nextflow.Session - [51/456ee1] Submitted process > PROCESS_HITS (batch_44)
Jun-01 21:02:44.051 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/1743eaa7f46875d2897b1a08985748]
Jun-01 21:02:44.066 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:44.067 [Task submitter] INFO  nextflow.Session - [2b/187ada] Submitted process > PROCESS_HITS (batch_49)
Jun-01 21:02:45.064 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/94/1ceafc3d91c04e8ea137479cea46f8]
Jun-01 21:02:45.079 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:45.080 [Task submitter] INFO  nextflow.Session - [87/fb8531] Submitted process > PROCESS_HITS (batch_45)
Jun-01 21:02:46.847 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/49/0ce37977d8de22a809ae9b5a1c0540]
Jun-01 21:02:46.863 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:46.863 [Task submitter] INFO  nextflow.Session - [65/6d57f1] Submitted process > PROCESS_HITS (batch_62)
Jun-01 21:02:47.204 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/e4c71a326c7912cc4c121e14b9995d]
Jun-01 21:02:47.231 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:47.232 [Task submitter] INFO  nextflow.Session - [98/e0455f] Submitted process > PROCESS_HITS (batch_58)
Jun-01 21:02:47.836 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d3/73b5fb4b23b9dd50d64f3e30508e41]
Jun-01 21:02:47.855 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:47.855 [Task submitter] INFO  nextflow.Session - [15/1ad4a3] Submitted process > PROCESS_HITS (batch_42)
Jun-01 21:02:47.880 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a6/0804c50122d73bbf16f94e47f5d3ec]
Jun-01 21:02:47.899 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:47.900 [Task submitter] INFO  nextflow.Session - [5c/a9c129] Submitted process > PROCESS_HITS (batch_50)
Jun-01 21:02:47.931 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8d/45587528bbaf1ba7f2333143f2d1b7]
Jun-01 21:02:47.953 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:47.954 [Task submitter] INFO  nextflow.Session - [85/9fa495] Submitted process > PROCESS_HITS (batch_35)
Jun-01 21:02:48.091 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/48/199160ca98a9708459018ba0d50ef8]
Jun-01 21:02:48.108 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:48.109 [Task submitter] INFO  nextflow.Session - [20/ce07bb] Submitted process > PROCESS_HITS (batch_61)
Jun-01 21:02:48.260 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/b2766107d5ff365f6e0daae924d8c0]
Jun-01 21:02:48.277 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:48.278 [Task submitter] INFO  nextflow.Session - [b1/d7c4ea] Submitted process > PROCESS_HITS (batch_48)
Jun-01 21:02:48.571 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/da/543cadc2b8b1475d7d3a8f18c6b17b]
Jun-01 21:02:48.592 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:48.593 [Task submitter] INFO  nextflow.Session - [b7/0c175a] Submitted process > PROCESS_HITS (batch_43)
Jun-01 21:02:55.518 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0b/43e938da814dd90d2988ee74bd593d]
Jun-01 21:02:55.535 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:55.535 [Task submitter] INFO  nextflow.Session - [a5/598c40] Submitted process > PROCESS_HITS (batch_51)
Jun-01 21:02:55.882 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/99/bc8b23bc3b198e93c02077d75adf39]
Jun-01 21:02:55.903 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:55.903 [Task submitter] INFO  nextflow.Session - [df/449dcf] Submitted process > PROCESS_HITS (batch_63)
Jun-01 21:02:55.929 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/187adaf9d8cf5736360ad6542cd3b2]
Jun-01 21:02:55.942 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:55.942 [Task submitter] INFO  nextflow.Session - [90/3647eb] Submitted process > PROCESS_HITS (batch_39)
Jun-01 21:02:55.953 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/51/456ee1d2573bea0e5f2a7bc9cab638]
Jun-01 21:02:55.965 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:55.965 [Task submitter] INFO  nextflow.Session - [a7/a5e70c] Submitted process > PROCESS_HITS (batch_55)
Jun-01 21:02:56.029 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/c8dc441cba6cd39739b06c118b5178]
Jun-01 21:02:56.033 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/66/847bbfd948de1db53f0449313f5ae0]
Jun-01 21:02:56.043 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:56.043 [Task submitter] INFO  nextflow.Session - [5c/60b378] Submitted process > PROCESS_HITS (batch_37)
Jun-01 21:02:56.073 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:56.073 [Task submitter] INFO  nextflow.Session - [db/e61068] Submitted process > PROCESS_HITS (batch_53)
Jun-01 21:02:56.276 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/25c9657e1339ee2a25c47b077fdd50]
Jun-01 21:02:56.298 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:56.299 [Task submitter] INFO  nextflow.Session - [5f/46997e] Submitted process > PROCESS_HITS (batch_33)
Jun-01 21:02:56.879 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/87/fb8531f523061fc27e87af02e5952e]
Jun-01 21:02:56.898 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:56.898 [Task submitter] INFO  nextflow.Session - [04/1e2e31] Submitted process > PROCESS_HITS (batch_36)
Jun-01 21:02:58.497 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/65/6d57f19a2ce4e55c39aef43d5307ca]
Jun-01 21:02:58.527 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:58.528 [Task submitter] INFO  nextflow.Session - [ea/d59f5d] Submitted process > PROCESS_HITS (batch_38)
Jun-01 21:02:59.424 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/98/e0455fd3185b5cae0afa7890099494]
Jun-01 21:02:59.447 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:59.447 [Task submitter] INFO  nextflow.Session - [e3/ac5830] Submitted process > PROCESS_HITS (batch_56)
Jun-01 21:02:59.916 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/20/ce07bbb3e570780d1e1b5f7107f6e6]
Jun-01 21:02:59.931 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:59.931 [Task submitter] INFO  nextflow.Session - [d3/d86b03] Submitted process > PROCESS_HITS (batch_47)
Jun-01 21:02:59.964 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/1ad4a3156163c84cc51160b15c4c05]
Jun-01 21:02:59.980 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:02:59.980 [Task submitter] INFO  nextflow.Session - [dc/feb040] Submitted process > PROCESS_HITS (batch_59)
Jun-01 21:03:00.021 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5c/a9c129840a1cf66d5a2140ef8d371a]
Jun-01 21:03:00.058 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:03:00.058 [Task submitter] INFO  nextflow.Session - [5d/74a3df] Submitted process > PROCESS_HITS (batch_60)
Jun-01 21:03:00.358 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/85/9fa49550bfb8f72c0a351a48793680]
Jun-01 21:03:00.378 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:03:00.378 [Task submitter] INFO  nextflow.Session - [b6/7a204a] Submitted process > PROCESS_HITS (batch_46)
Jun-01 21:03:00.387 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b1/d7c4ead429ece9a549cee53a82ca44]
Jun-01 21:03:00.405 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:03:00.405 [Task submitter] INFO  nextflow.Session - [68/244dc8] Submitted process > PROCESS_HITS (batch_34)
Jun-01 21:03:00.886 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b7/0c175a86c0208d7b396c34a11ba7ef]
Jun-01 21:03:00.903 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:03:00.904 [Task submitter] INFO  nextflow.Session - [b2/b58005] Submitted process > PROCESS_HITS (batch_52)
Jun-01 21:03:08.507 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/598c40cb075d9c787bc87a245c989e]
Jun-01 21:03:08.530 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:03:08.530 [Task submitter] INFO  nextflow.Session - [68/4ec608] Submitted process > PROCESS_HITS (batch_66)
Jun-01 21:03:08.770 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a7/a5e70cb0a65bddf8f4dfe4d813b61a]
Jun-01 21:03:08.789 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:03:08.790 [Task submitter] INFO  nextflow.Session - [d6/fd3e59] Submitted process > PROCESS_HITS (batch_64)
Jun-01 21:03:08.913 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5c/60b378751ea82666e00de76b431bca]
Jun-01 21:03:08.979 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/db/e6106882418356ce1bb8b87c0719fd]
Jun-01 21:03:09.078 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/90/3647eb396694b0552e32c07d4f13d5]
Jun-01 21:03:09.301 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5f/46997e12da27ac5ec26e48fc208a52]
Jun-01 21:03:09.410 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/04/1e2e31a6eef47851e3dfdeb941a29b]
Jun-01 21:03:09.525 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/df/449dcf962fb1e59c5b5c08c05d04f7]
Jun-01 21:03:10.457 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/4ec608d43af49be9bce7868f15ebdc]
Jun-01 21:03:11.345 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ea/d59f5dd172519d9745caf241ad7995]
Jun-01 21:03:12.026 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e3/ac58303a2052247268a37c987a73cd]
Jun-01 21:03:12.578 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dc/feb0405b19be11e268609f3aca2d9f]
Jun-01 21:03:12.731 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d3/d86b0348e73c1e5696fc0612259231]
Jun-01 21:03:12.840 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/244dc843d870a30305b5f684b5703b]
Jun-01 21:03:13.068 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b6/7a204a006daae06fdbe79ce9c41716]
Jun-01 21:03:13.196 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5d/74a3df627060e3ebe3271a054592a1]
Jun-01 21:03:13.277 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b2/b58005b8ae731634fabacb11edaee8]
Jun-01 21:03:20.739 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d6/fd3e5945c1064a0b51e4cf21e3a4ed]
Jun-01 21:03:36.973 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > No more task to compute -- The following nodes are still active:
[process] CREATE_DATAFRAME
  status=ACTIVE
  port 0: (value) bound ; channel: csv_files
  port 1: (queue) closed; channel: classification
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: batch_count
  port 4: (cntrl) -     ; channel: $

[process] AUTOENCODER
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: input_csv
  port 1: (queue) OPEN  ; channel: scaler
  port 2: (queue) OPEN  ; channel: model
  port 3: (queue) OPEN  ; channel: script
  port 4: (cntrl) -     ; channel: $

[process] MODEL_PREDICT
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: encoded_features
  port 1: (queue) OPEN  ; channel: ml_model
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: version
  port 4: (cntrl) -     ; channel: $

Jun-01 21:04:51.459 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:04:51.460 [Task submitter] INFO  nextflow.Session - [c5/54fec3] Submitted process > CREATE_DATAFRAME (1)
Jun-01 21:08:37.062 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/54fec3ce9be6d674637be26f4d20c3]
Jun-01 21:13:37.108 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/54fec3ce9be6d674637be26f4d20c3]
Jun-01 21:18:37.188 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/54fec3ce9be6d674637be26f4d20c3]
Jun-01 21:23:37.223 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/54fec3ce9be6d674637be26f4d20c3]
Jun-01 21:28:37.268 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/54fec3ce9be6d674637be26f4d20c3]
Jun-01 21:33:37.317 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/54fec3ce9be6d674637be26f4d20c3]
Jun-01 21:36:15.005 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c5/54fec3ce9be6d674637be26f4d20c3]
Jun-01 21:36:15.046 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:36:15.046 [Task submitter] INFO  nextflow.Session - [f0/293bde] Submitted process > AUTOENCODER (1)
Jun-01 21:37:21.757 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: AUTOENCODER (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f0/293bde3e2b77e626684b9334bf0f6c]
Jun-01 21:37:21.848 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-01 21:37:21.848 [Task submitter] INFO  nextflow.Session - [60/60141b] Submitted process > MODEL_PREDICT (1)
Jun-01 21:37:23.960 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 136; name: MODEL_PREDICT (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/60/60141b1975967b2d54cc92f9bb9bb6]
Jun-01 21:37:23.965 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-01 21:37:24.060 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-01 21:37:24.060 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-01 21:37:24.067 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jun-01 21:37:24.074 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
Jun-01 21:37:24.079 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=136; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=4h 27m 27s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=21; peakCpus=42; peakMemory=84 GB; ]
Jun-01 21:37:24.138 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-01 21:37:24.159 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-01 21:37:24.159 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
